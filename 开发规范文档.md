# Evolve - 开发规范文档 v2.5 (Context一致性强化版)

**📅 更新时间：2025-01-06 23:45:00**
**🎯 更新重点：强化SwiftData Context一致性规范、明确拒绝统一数据管理器方案、补充共享Container最佳实践**

## 引言

本规范是Evolve iOS应用AI辅助开发的最高行为准则。AI在生成任何代码、UI元素、文档或提出任何建议时，必须严格遵守本规范。本规范整合了产品设计、UI设计、技术架构的要求，并基于项目实际开发经验，增强了架构一致性约束和AI集成规范，旨在确保AI产出高度一致、符合规范、易于维护，并能顺利实现项目目标。

**⚠️ 重要更新**：基于SwiftData Context冲突问题分析，本版本新增：
- SwiftData Context一致性强制规范
- 共享Container架构最佳实践
- 明确拒绝统一数据管理器方案
- Context冲突预防和检测机制

## 一、AI开发约束与输出质量控制

### 1. AI角色定位与交互规范

- **核心角色**：AI必须作为"私人教练、引导者、对话伙伴、赋能者"，而非简单的工具或助手。
- **对话优先原则**：
  - 所有核心功能优先采用对话式交互设计
  - AI回复必须具备情感温度和个性化特征
  - 支持多轮对话和上下文记忆
  - 避免机械化的模板回复
- **情境感知要求**：
  - AI必须能够识别用户当前的情绪状态
  - 根据对话上下文调整回复风格和内容
  - 考虑用户的个性偏好和历史行为
  - 在适当时机主动提供支持和建议

### 2. AI成本控制与功能平衡规范

- **智能分层策略**：
  - **核心AI体验场景**：用户主动对话、情绪支持、危机干预（保证AI个性化回复）
  - **混合处理场景**：每日洞察、习惯建议（AI生成+本地模板优化）
  - **本地优先场景**：基础统计、简单提醒（本地处理，必要时AI增强）
- **个性化保障机制**：
  - 核心对话功能必须保持AI个性化特征
  - 通过智能缓存和模板系统降低重复调用成本
  - 本地智能引擎学习用户偏好，减少AI依赖
- **成本优化策略**：
  - 用户画像缓存7天，减少重复分析
  - 相似场景回复复用，避免重复生成
  - 分时段调用策略，在用户活跃时段提供完整AI体验

### 3. 智能分析引擎开发规范

- **行为模式检测**：
  - 识别用户习惯完成的时间规律（本地算法处理）
  - 检测连续失败模式和触发因素（AI深度分析）
  - 分析用户交互偏好和成功因素（混合处理）
- **情绪状态分析**：
  - 基于文本输入分析情绪状态（AI处理）
  - 通过行为数据推断情绪变化（本地+AI结合）
  - 识别需要情绪支持的关键时机（AI处理）
- **预测性洞察**：
  - 预测用户今日习惯完成概率（本地算法）
  - 生成个性化的每日洞察（AI生成+模板结合）
  - 预警潜在的习惯中断风险（AI分析）

### 4. 代码质量与可维护性原则

- **简洁性**：代码应简洁、高效、易读，避免不必要的复杂性。
- **一致性**：代码风格、命名规范、文件结构保持一致。
- **可测试性**：设计便于单元测试和UI测试的代码结构。
- **可维护性**：清晰的代码组织和注释，便于后期维护和扩展。
- **🚨 调试代码清理**：**严禁在生产代码中保留print()调试语句**，所有调试代码必须在提交前清理。

### 5. 严格遵循已定方案

- **唯一依据**：以提供的最新版本项目文档（产品需求文档、UI设计规范文档、技术架构文档）为唯一依据。
- **文档优先级**：文档之间如有冲突，以本开发规范为最高优先级。其次是技术架构文档，然后是产品需求文档和UI设计规范。
- **禁止幻想与臆测**：所有产出必须有据可依，不得自行创造未明确的功能、UI样式、数据结构或业务逻辑。

## 一点五、AI代码审查专项规范（基于问题反馈优化）

### 🚨 核心原则：分层系统审查，避免分批发现问题

**强制要求**：
- **一次性全面审查**：禁止"发现一批问题→修复→再发现新问题"的循环模式
- **分层优先级审查**：按架构层级从底层到上层系统审查
- **工具调用优化**：在工具调用限制内最大化审查覆盖面

### 1. 分层审查策略（强制执行顺序）

**阶段1：架构基础层（最高优先级）**
- 🔍 检查项：SwiftData关系模式、外键违规、Repository完整性
- 🛠️ 检查方法：grep搜索关键模式 + 文件结构分析
- ⚡ 优先级：🔴 严重架构违规必须优先处理

**阶段2：服务层架构（高优先级）**
- 🔍 检查项：单例模式、ModelContext直接使用、依赖注入
- 🛠️ 检查方法：搜索违规模式 + Service文件审查
- ⚡ 优先级：🟡 影响架构一致性

**阶段3：视图层合规（中优先级）**
- 🔍 检查项：@MainActor标记、Sheet状态管理、MVVM规范
- 🛠️ 检查方法：ViewModel文件检查 + UI组件审查
- ⚡ 优先级：🟢 影响代码质量

### 2. 快速违规检测方法

**关键模式搜索**：
```bash
# 外键模式违规检测
grep -r "var.*Id: UUID" --include="*.swift"
grep -r "userId\|habitId\|profileId" --include="*.swift"

# 单例模式违规检测  
grep -r "static let shared" --include="*.swift"
grep -r "\.shared\." --include="*.swift"

# ModelContext直接使用违规
grep -r "@Environment(\.modelContext)" --include="*.swift"
grep -r "private.*modelContext" --include="*.swift"
```

### 3. 审查报告标准格式

**强制输出格式**：
```
## 🔍 系统性架构审查报告

### 🔴 严重问题（必须立即修复）
- [具体问题] - 影响：[架构/性能/安全]
- 涉及文件：[文件列表]

### 🟡 重要问题（影响架构一致性）
- [具体问题] - 影响：[维护性/扩展性]

### 🟢 一般问题（代码质量优化）
- [具体问题] - 影响：[代码风格/性能]

### 📋 系统性修复计划
**批次1（架构基础）**：[文件列表] - [修复内容]
**批次2（服务层）**：[文件列表] - [修复内容]
**批次3（视图层）**：[文件列表] - [修复内容]
```

### 4. 工具调用限制应对策略

**优化策略**：
- **并行调用**：同时执行多个read_file和grep_search
- **重点文件优先**：优先审查核心架构文件
- **模式检测优先**：使用grep快速发现违规模式
- **分批审查**：如遇限制，按优先级分批完成

**⚠️ 审查质量保障**：
- 每次审查必须覆盖所有架构层级
- 发现问题必须分析根因和影响范围
- 修复方案必须是系统性的，避免单点修复

## 二、SwiftData版本兼容性开发规范

### 1. Swift编码规范

- **代码风格**：四空格缩进，每行不超过120个字符
- **命名约定**：小驼峰式变量/函数，大驼峰式类型
- **安全编程**：使用可选类型，避免强制解包
- **🚨 调试代码清理**：严禁保留print()调试语句

### 2. SwiftUI开发规范

- **状态管理最佳实践**：
  - @State：单一视图本地状态
  - @Binding：双向数据绑定
  - @ObservedObject/@StateObject：ViewModel引用
  - @Environment：全局依赖注入
- **视图组织**：复杂视图分解为子组件，使用ViewModifier封装样式

### 2.1 SwiftUI Sheet状态管理规范（iOS 17.0+兼容）

#### 🚨 核心原则：单一状态源管理

**强制要求（解决多重Sheet冲突）**：
- **统一状态管理**：所有Sheet状态必须在单一ViewModel中集中管理，避免分散在多个@State变量中
- **枚举驱动模式**：使用枚举定义所有可能的Sheet类型，确保同时只有一个Sheet处于活跃状态
- **@MainActor线程安全**：所有Sheet状态管理必须在主线程执行，与项目MVVM架构保持一致

#### 🔒 标准实现模式（遵循EA命名规范）

**核心实现要求**：
```swift
// ✅ 正确：统一Sheet状态枚举
enum EASheetType: Identifiable {
    case habitCreation, habitDetail(EAHabit), settings
    var id: String { /* ... */ }
}

// ✅ 正确：Sheet管理器
@MainActor
class EASheetManager: ObservableObject {
    @Published var activeSheet: EASheetType?
    func presentSheet(_ sheetType: EASheetType) { activeSheet = sheetType }
    func dismissSheet() { activeSheet = nil }
}

// ✅ 正确：统一Sheet管理
.sheet(item: $sheetManager.activeSheet) { sheetType in
    sheetContent(for: sheetType)
}
```

#### 🚫 严格禁止的做法

**多个@State变量管理Sheet**：
```swift
// ❌ 错误：多个Sheet状态变量（导致冲突）
struct BadExampleView: View {
    @State private var showHabitCreation = false
    @State private var showUserProfile = false
    // 这种方式会导致多重Sheet冲突
}
```

#### ⚡ 性能优化要求

- **延迟加载**：Sheet内容在需要时才创建，使用@ViewBuilder确保懒加载
- **主线程执行**：所有Sheet状态变更必须在主线程执行
- **内存管理**：Sheet关闭时及时释放相关资源

**⚠️ 重要提醒**：
- 所有Sheet状态管理必须遵循项目的EA命名前缀规范
- 必须与项目的MVVM架构和@MainActor要求保持一致
- 严禁在生产代码中保留调试print()语句

### 3. MVVM实现规范

- **ViewModel要求**：
  - 必须标记@MainActor确保UI更新在主线程
  - 使用@Published暴露状态
  - 不直接引用View，保持单向依赖
- **错误处理**：统一的错误类型和用户友好的错误消息

### 4. SwiftData开发规范

#### 🚨 关键约束（iOS 17.0-18.5+兼容性）

**核心规则（绝对不可妥协）**：
- **单端inverse规则**：双向关系只在一端使用@Relationship(inverse:)，另一端用普通属性，SwiftData自动维护关系完整性
- **关系赋值顺序（iOS 18+强制要求）**：创建对象 → 插入ModelContext → 赋值关系属性 → 保存Context
- **UUID自动生成**：不在init中手动设置UUID，让系统自动生成
- **关系集合初始化**：所有集合类型的关系属性必须在init中初始化，而非设置默认值

- **🔒 Apple官方ModelContainer配置规范（WWDC 2023/2024确认）**：
  ```swift
  // ✅ 正确：多模型使用可变参数语法（Apple官方标准）
  let container = try ModelContainer(for: 
      EAUser.self,
      EAHabit.self,
      EACompletion.self,
      EAUserSettings.self
  )
  
  // ✅ 正确：单模型直接传递
  let container = try ModelContainer(for: EAUser.self)
  
  // ❌ 错误：使用数组语法（编译错误）
  let container = try ModelContainer(for: [EAUser.self, EAHabit.self]) // 编译错误
  ```

- **🔒 Apple官方Preview配置规范（WWDC 2024确认）**：
  ```swift
  // ✅ 正确：SwiftUI Preview必须配置ModelContainer
  struct SampleData: PreviewModifier {
      static func makeSharedContext() throws -> ModelContainer {
          let config = ModelConfiguration(isStoredInMemoryOnly: true)
          let container = try ModelContainer(for: 
              EAUser.self,
              EAHabit.self,
              EACompletion.self,
              configurations: config
          )
          return container
      }
      
      func body(content: Content, context: ModelContainer) -> some View {
          content.modelContainer(context)
      }
  }
  
  // ✅ 使用方式
  #Preview(traits: .sampleData) {
      ContentView()
  }
  ```

- **🔒 完整双向inverse关系原则（绝对不可妥协）**：
  ```swift
  // ✅ 正确：一端使用@Relationship(inverse:)，另一端使用普通属性
  @Model
  class EAHabit {
      var id: UUID = UUID()
      var name: String
      var creationDate: Date = Date()
      var iconName: String
      var targetFrequency: Int
      var isActive: Bool = true
      
      // ✅ 正确：定义inverse指向另一端的属性（不设置默认值）
      @Relationship(deleteRule: .cascade, inverse: \EACompletion.habit) 
      var completions: [EACompletion]
      
      // ✅ 正确：反向关系使用普通属性
      var user: EAUser?
      
      // ✅ 正确：SwiftData原生支持基础类型数组（iOS 18.2+日志警告是系统级问题）
      var selectedWeekdays: [Int] = []
      var reminderTimes: [String] = []
      
      init(name: String, iconName: String, targetFrequency: Int) {
          self.name = name
          self.iconName = iconName
          self.targetFrequency = targetFrequency
          self.completions = [] // 在init中初始化，而非属性默认值
      }
  }
  
  @Model
  class EACompletion {
      var id: UUID = UUID()
      var date: Date = Date()
      var completionNote: String?
      var energyLevel: Int = 5 // 1-10
      
      // ✅ 正确：使用普通属性，SwiftData自动维护inverse关系
      var habit: EAHabit?
      
      init(completionNote: String? = nil, energyLevel: Int = 5) {
          self.completionNote = completionNote
          self.energyLevel = energyLevel
      }
  }
  
  @Model
  class EAUser {
      var id: UUID = UUID()
      var username: String
      var email: String?
      var creationDate: Date = Date()
      
      // ✅ 正确：定义inverse指向另一端的属性（不设置默认值）
      @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
      var habits: [EAHabit]
      
      init(username: String, email: String? = nil) {
          self.username = username
          self.email = email
          self.habits = [] // 在init中初始化，而非属性默认值
      }
  }
  ```

- **🚨 关系设计核心规则**：
  - **单端inverse规则**：双向关系只在一端使用@Relationship(inverse:)，另一端用普通属性声明
  - **类型匹配要求**：inverse的KeyPath必须精确指向对方模型的对应属性
  - **关系基数正确**：一对一、一对多、多对多关系的类型必须完全匹配
  - **属性名一致性**：关系两端的属性名必须精确对应，包括大小写和单复数
  - **🔒 核心原则（绝对不可妥协）**：双向关系只在一端使用@Relationship(inverse:)，另一端用普通属性声明，SwiftData自动维护关系完整性
- **强制要求**：必须在一端使用@Relationship(inverse:)，另一端使用普通属性（类型必须匹配）
- **绝对禁止**：两端同时使用@Relationship，会导致关系冲突和循环引用

- **🚫 严格禁止的做法**：
  - **删除inverse参数**：绝对不能通过删除inverse来"解决"编译错误
  - **两端同时使用@Relationship**：会导致关系冲突和循环引用
  - **完全省略inverse**：双向关系必须在一端正确定义inverse
  - **单向关系设计**：需要双向访问的关系必须正确定义关系对
  - **关系默认值设置**：@Model会忽略关系属性的默认值（如var crews: [CrewModel] = []），实际值由SwiftData管理。关系属性不应设置默认值，应在init中初始化
  - **构造器中创建关联对象**：避免在init中创建关联对象，会导致运行时错误（Failed to find container）

- **🛠️ 关系定义最佳实践**：
  ```swift
  // ✅ 正确示例（参考Apple官方Flight和CrewModel模式）
  @Model class Flight {
      // 正确定义逆向指向CrewModel的flight属性
      @Relationship(inverse: \CrewModel.flight) 
      var crews: [CrewModel] // 一对多关系，不设置默认值
      
      init() {
          self.crews = [] // 在init中初始化
      }
  }
  @Model class CrewModel {
      var flight: Flight? // 普通属性，无需@Relationship
  }
  
  // ✅ 项目中的一对一关系示例
  @Model class EAUser {
      @Relationship(deleteRule: .cascade, inverse: \EAUserSettings.user)
      var settings: EAUserSettings?
  }
  @Model class EAUserSettings {
      var user: EAUser? // 普通属性，无需@Relationship
  }
  
  // ✅ 项目中的一对多关系示例
  @Model class EAUser {
      @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
      var habits: [EAHabit]
      
      init(username: String) {
          self.habits = [] // 在init中初始化
      }
  }
  @Model class EAHabit {
      var user: EAUser? // 普通属性，无需@Relationship
  }
  
  // ✅ 避免多对多关系，使用中间模型
  // 不推荐：直接多对多关系
  // 推荐：通过中间模型实现（如FlightCrewAssignment）
  @Model class EAHabitTagAssignment {
      var habitId: UUID
      var tagId: UUID
      var assignedDate: Date = Date()
  }
  ```

- **性能优化规范**：
  - **简单@Query使用**：仅用于简单谓词，避免复杂逻辑导致iOS 18+性能问题
  - **复杂查询@ModelActor处理**：使用@ModelActor处理复杂数据操作，避免@Query性能问题
  - **批量操作优化**：使用ModelContext.transaction提高性能
  - **内存管理**：及时释放大型查询结果，避免内存泄漏

- **🔍 常见错误预防**：
  ```swift
  // ❌ 错误：两端都使用@Relationship（违反单端inverse规则）
  @Model class EAHabit {
      @Relationship(inverse: \EAUser.habits) // 冗余，删除此行
      var user: EAUser?
  }
  
  // ❌ 错误：KeyPath指向错误的属性
  @Relationship(inverse: \EAUser.habit) // 错误：应为habits
  
  // ❌ 错误：在构造器中创建关联对象
  init() {
      self.tag = Tag(name: "default") // 错误：未插入Context
  }
  // ✅ 正确：先插入Context再赋值关系
  let tag = Tag(name: "default")
  modelContext.insert(tag)
  self.tag = tag
  ```

- **版本兼容性保障**：
  - **关系完整性检查**：定期验证所有关系的inverse定义正确性
  - **Schema迁移计划**：每次模型变更都要更新SchemaMigrationPlan
  - **开发阶段数据库重置**：模型变更后强制删除数据库文件，重新创建干净环境
  - **生产环境迁移策略**：使用版本化迁移，支持CloudKit同步
  - **线程安全要求**：所有SwiftData操作必须在主线程执行（用@MainActor或DispatchQueue.main），避免并发冲突
  - **删除规则合理配置**：使用@Relationship的删除规则（.cascade：删除父对象时自动删除子对象；.noAction：手动管理依赖）
  - **自动推断限制认知**：SwiftData仅在特定场景下自动推断逆向关系（如两端均为Optional的一对一关系），其他情况必须显式声明inverse。需显式声明inverse的场景包括：关系一端为Non-Optional、多对多关系（两端均为Non-Optional）
  - **调试工具应用**：使用Xcode的Memory Graph Debugger检测循环引用，开启com.apple.CoreData.ConcurrencyDebug 1捕获线程问题

- **🛠️ 最佳实践**：
  ```swift
  // ✅ 简单查询使用@Query
  @Query(filter: #Predicate<EAHabit> { $0.isActive == true }) 
  var activeHabits: [EAHabit]
  
  // ✅ 复杂查询使用@ModelActor
  @ModelActor
  actor EADataManager {
      func fetchHabits() throws -> [EAHabit] {
          let descriptor = FetchDescriptor<EAHabit>(...)
          return try modelContext.fetch(descriptor)
      }
  }
  ```

- **🚫 严格禁止的做法**：
  - **删除inverse参数**：绝对不能通过删除inverse来"解决"编译错误
  - **单向关系设计**：所有双向关系必须在两端正确定义
  - **关系默认值设置**：@Model会忽略关系属性的默认值
  - **构造器中创建关联对象**：避免在init中创建关联对象，会导致运行时错误
  - **两端同时使用@Relationship**：会导致关系冲突和循环引用

## 三、项目结构与命名规范

### 1. 文件结构规范

严格遵循《技术架构文档》中定义的目录结构，关键目录如下：

- **Evolve/Core/**: 核心服务和共享功能
- **Evolve/Common/**: 通用工具和扩展
- **Evolve/Features/**: 按业务划分的功能模块
- **Evolve/UIComponents/**: 可复用UI组件

任何在此结构下新增的主要组件或模块，AI均需在README.md中记录。

### 2. 命名规范

- **文件命名**：
  - 一个文件一个主要类型，文件名反映主要内容。
  - 使用后缀表示文件类型，如:
    - `xxxView.swift`: SwiftUI视图
    - `xxxViewModel.swift`: 视图模型
    - `xxxService.swift`: 服务类
    - `xxx+Extension.swift`: 扩展
- **类型命名**：
  - **模型**：名词，如`Habit`
  - **视图**：以"View"结尾，如`HabitDetailView`
  - **视图模型**：以"ViewModel"结尾，如`HabitDetailViewModel`
  - **服务**：以"Service"或"Manager"结尾，如`NotificationService`
  - **组件**：反映功能，如`EnergyFlowProgressBar`
- **方法命名**：
  - 使用动词开头，如`fetchHabits()`、`completeHabit(id:)`
  - 异步方法明确标识，如`async loadData()`
  - 事件处理方法应表明时机，如`onAppear()`、`didTapButton()`
- **属性命名**：
  - 使用名词，如`habits`、`userName`
  - 布尔值应表明状态，如`isLoading`、`hasData`
  - 私有属性使用_前缀，如`private var _cache`

### 2.1 命名前缀与防冲突策略

**核心命名规范**：
- **EA前缀原则**：项目自定义类型统一使用`EA`前缀（代表 Evolve App）
- **简洁性原则**：保持命名简洁易读，避免过长的多级前缀

**标准命名指南**：
- **数据模型**：`EAHabit`、`EACompletion`、`EAUser`
- **视图组件**：`EAButton`、`EATextField`、`EAHabitCard`
- **服务类**：`EANetworkService`、`EAAIService`
- **视图模型**：`EAHabitViewModel`、`EAUserViewModel`

**命名冲突处理**：
- **扩展前缀**：仅对易冲突类型使用细分标识
  - `Message` → `EAAIMessage`
  - `Task` → `EAHabitTask`
  - `Event` → `EAAnalyticsEvent`

**文件命名要求**：
- 文件名必须与主要Swift类型保持一致：`EAButton.swift`
- 扩展文件使用清晰命名：`Date+EAFormatting.swift`

**SwiftData模型特殊处理**：
- 模型命名保持简洁：`EAHabit`（无需Model后缀）
- 关系命名反映关联：`habit.completions`

### 3. 组件复用规范

- **抽取阈值**：
  - 当UI元素在3个或以上地方重复使用时，应封装为组件。
  - 当业务逻辑超过50行代码时，考虑拆分为多个辅助方法。
- **参数传递**：
  - 使用明确的参数传递组件配置，避免全局状态。
  - 回调函数使用闭包，如`onCompletion: @escaping () -> Void`。
- **组件接口**：
  - 定义清晰的组件API表面，隐藏内部实现细节。
  - 使用@ViewBuilder提供内容定制能力。
  - 支持SwiftUI环境值传播。
- **新组件记录**：AI创建的任何可复用组件（UI组件、服务等），都必须在README.md中记录其名称、完整路径（相对于项目根目录，例如 `Evolve/UIComponents/EAButton.swift`）及简要功能。

## 四、架构一致性强制规范（新增核心约束）

### 🚨 关键原则：统一架构模式，禁止混合架构

基于项目架构审查结果，发现架构不一致问题严重影响代码质量和维护性。以下规范为**强制执行**，不得违反。

### 0. SwiftData Context一致性强制规范（iOS 18+关键要求）

#### 0.1 Context冲突问题根因分析

**核心问题**：
- **多个独立@ModelActor**：每个Repository都是独立的@ModelActor，拥有各自的ModelContext实例
- **跨Context关系操作**：iOS 18+严格禁止跨Context的关系赋值操作，会导致应用崩溃
- **数据不一致风险**：多个Context可能导致数据同步问题和查询结果不一致

**Apple官方指导**：
- **最小Container原则**：应用中应使用尽可能少的ModelContainer实例
- **Context共享策略**：相关的数据操作应共享同一个ModelContext
- **线程安全保障**：通过@ModelActor确保线程安全，但避免过度分散Context

#### 0.2 强制要求：共享Container架构模式

**🔒 核心架构原则（强制执行）**：
```swift
// ✅ 正确：共享Container模式
@main
struct EvolveApp: App {
    // 全局共享的ModelContainer
    let sharedContainer: ModelContainer
    
    init() {
        do {
            // 创建共享Container，所有Repository使用同一个
            sharedContainer = try ModelContainer(for:
                EAUser.self,
                EAHabit.self,
                EACompletion.self,
                EAUserSettings.self,
                EAUserSocialProfile.self,
                EAUserModerationProfile.self,
                EACommunityPost.self,
                EACommunityComment.self,
                EACommunityLike.self,
                EACommunityFollow.self,
                EAAIInsight.self
            )
        } catch {
            fatalError("Failed to create ModelContainer: \(error)")
        }
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .modelContainer(sharedContainer) // 全局共享
        }
    }
}

// ✅ 正确：Repository使用共享Container
@ModelActor
actor EAUserRepository: EAUserRepositoryProtocol {
    // 使用传入的共享ModelContext，而非独立创建
    
    func fetchUser(by id: UUID) async throws -> EAUser? {
        let descriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == id }
        )
        return try modelContext.fetch(descriptor).first
    }
}

// ✅ 正确：Repository容器管理共享Context
class EARepositoryContainer: ObservableObject {
    let userRepository: EAUserRepository
    let habitRepository: EAHabitRepository
    let communityRepository: EACommunityRepository
    
    init(modelContainer: ModelContainer) {
        // 所有Repository共享同一个Container
        self.userRepository = EAUserRepository(modelContainer: modelContainer)
        self.habitRepository = EAHabitRepository(modelContainer: modelContainer)
        self.communityRepository = EACommunityRepository(modelContainer: modelContainer)
    }
}
```

#### 0.3 严格禁止：统一数据管理器方案

**🚫 明确拒绝的架构模式**：
```swift
// ❌ 严格禁止：统一数据管理器模式
class EAUnifiedDataManager {
    static let shared = EAUnifiedDataManager() // 禁止单例
    private let modelContext: ModelContext
    
    // 禁止：将所有数据操作集中到一个类中
    func createUser() { /* ... */ }
    func createHabit() { /* ... */ }
    func createPost() { /* ... */ }
    // 违反单一职责原则，增加复杂度
}

// ❌ 严格禁止：替换现有Repository架构
// 不得删除或重构现有的Repository模式
```

**拒绝理由**：
1. **违反单一职责原则**：一个类承担过多职责，难以维护
2. **增加架构复杂度**：破坏现有的模块化设计
3. **测试困难**：统一管理器难以进行单元测试
4. **扩展性差**：新功能需要修改核心管理器类
5. **重构成本高**：需要大量修改现有代码

#### 0.4 推荐方案：共享Container优化

**✅ 最佳实践方案**：
- **保持现有Repository架构**：不改变Repository接口和实现
- **共享ModelContainer**：所有Repository使用同一个Container实例
- **最小修改原则**：只需修改Container的创建和传递方式
- **零风险优化**：不影响现有功能，只解决Context冲突问题

**实施步骤**：
1. **修改App入口**：创建共享ModelContainer
2. **更新Repository容器**：传递共享Container给所有Repository
3. **验证Context一致性**：确保所有Repository使用同一Context
4. **测试数据操作**：验证跨Repository的关系操作正常

#### 0.5 Context一致性检测机制

**强制检查清单**：
- [ ] 所有Repository是否使用同一个ModelContainer？
- [ ] 是否存在独立创建的ModelContext实例？
- [ ] 跨Repository的关系操作是否正常？
- [ ] 是否消除了Context不一致导致的崩溃？

**违规检测模式**：
```swift
// ❌ 违规：独立创建ModelContext
@ModelActor
actor BadRepository {
    private let modelContext = ModelContext(...) // 禁止独立创建
}

// ❌ 违规：多个Container实例
let container1 = try ModelContainer(for: EAUser.self)
let container2 = try ModelContainer(for: EAHabit.self) // 禁止多Container
```

**⚠️ 关键警告**：
- **绝对禁止**：创建统一数据管理器替代Repository架构
- **强制要求**：所有Repository必须共享同一个ModelContainer
- **性能保障**：共享Container提升内存使用效率和查询性能
- **稳定性保障**：消除iOS 18+环境下的Context冲突崩溃风险

### 1. Repository模式强制执行规范

#### 1.1 数据访问层强制约束

**🔒 强制要求（绝对不可违反）**：
- **数据修改必须通过Repository层**：所有数据的创建、更新、删除操作必须通过Repository
- **禁止Service+ModelContext模式**：任何业务服务不得直接操作ModelContext
- **统一依赖注入**：所有Repository通过EARepositoryContainer统一管理和注入
- **SwiftUI视图层例外**：允许使用@Query进行简单的只读数据查询

**✅ 正确的数据访问模式**：
```swift
// ✅ 正确：ViewModel通过Repository访问数据
@MainActor
class EACommunityViewModel: ObservableObject {
    private let communityRepository: EACommunityRepositoryProtocol
    
    init(communityRepository: EACommunityRepositoryProtocol) {
        self.communityRepository = communityRepository
    }
    
    func loadPosts() async {
        posts = await communityRepository.fetchRecentPosts()
    }
}

// ✅ 正确：Repository使用@ModelActor操作数据
@ModelActor
actor EACommunityRepository: EACommunityRepositoryProtocol {
    func fetchRecentPosts() async -> [EACommunityPost] {
        let descriptor = FetchDescriptor<EACommunityPost>(...)
        return try? modelContext.fetch(descriptor) ?? []
    }
}
```

**❌ 严格禁止的做法**：
```swift
// ❌ 错误：ViewModel直接注入ModelContext
@MainActor  
class BadCommunityViewModel: ObservableObject {
    @Environment(\.modelContext) private var modelContext // 禁止
    
    func loadPosts() {
        let descriptor = FetchDescriptor<EACommunityPost>(...)
        posts = try? modelContext.fetch(descriptor) ?? [] // 禁止直接操作
    }
}

// ❌ 错误：Service直接操作ModelContext
class BadCommunityService {
    private let modelContext: ModelContext // 禁止
    
    func createPost() {
        modelContext.insert(post) // 禁止直接操作
    }
}
```

#### 1.2 Repository实现标准

**必要组件**：
- **Repository协议**：定义数据访问接口
- **Repository实现**：使用@ModelActor确保线程安全
- **Repository容器**：通过EARepositoryContainer统一管理
- **依赖注入**：通过Environment传递Repository

**🚨 基于渐进式优化的强制要求**：
```swift
// ✅ 强制模式：所有Repository必须通过容器管理
protocol EARepositoryContainer {
    var userRepository: EAUserRepositoryProtocol { get }
    var habitRepository: EAHabitRepositoryProtocol { get }
    var communityRepository: EACommunityRepositoryProtocol { get }
    // 新增Repository必须在此注册
}

// ✅ 强制依赖注入模式
@MainActor
class EAUserProfileViewModel: ObservableObject {
    @Environment(\.repositoryContainer) private var repositories
    
    func updateProfile() async {
        // 强制通过Repository访问数据
        await repositories.userRepository.updateSocialProfile(...)
    }
}
```

**实现检查清单**：
- [ ] 是否定义了清晰的Repository协议？
- [ ] 是否使用@ModelActor确保线程安全？
- [ ] 是否通过EARepositoryContainer管理？
- [ ] 是否避免了直接ModelContext访问？
- [ ] **新增**：是否遵循关系模式而非外键模式？
- [ ] **新增**：是否所有分离模型都有安全操作方法？

### 2. 数据模型复杂度控制规范

#### 2.1 @Relationship数量限制

**🚨 强制限制（性能保障）**：
- **单个模型@Relationship不得超过5个**：超过此限制将导致查询性能问题
- **复杂模型必须拆分**：超过5个关系的模型必须拆分为多个相关模型
- **使用关联模型**：通过一对一关系连接相关模型，而非直接包含所有关系

**✅ 正确的模型设计模式**：
```swift
// ✅ 正确：核心用户模型保持简洁
@Model
final class EAUser {
    var id: UUID = UUID()
    var username: String
    var email: String?
    
    // 核心关系（≤5个）
    @Relationship(deleteRule: .cascade, inverse: \EAUserSettings.user)
    var settings: EAUserSettings?
    
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit]
    
    // 通过关联模型访问社区数据
    @Relationship(deleteRule: .cascade, inverse: \EACommunityProfile.user)
    var communityProfile: EACommunityProfile?
}

// ✅ 正确：社区数据独立模型
@Model
final class EACommunityProfile {
    var id: UUID = UUID()
    var displayName: String?
    var bio: String?
    
    // 社区相关关系集中在此模型
    @Relationship(deleteRule: .cascade, inverse: \EACommunityPost.author)
    var posts: [EACommunityPost]
    
    @Relationship(deleteRule: .cascade, inverse: \EACommunityFollow.follower)
    var following: [EACommunityFollow]
    
    @Relationship(deleteRule: .cascade, inverse: \EACommunityFollow.followed)
    var followers: [EACommunityFollow]
    
    // 与核心用户模型的关系
    var user: EAUser?
}
```

**❌ 严格禁止的做法**：
```swift
// ❌ 错误：单个模型包含过多关系（>5个）
@Model
final class OverloadedUser {
    // 8个@Relationship关系 - 超过限制！
    @Relationship var settings: EAUserSettings?
    @Relationship var habits: [EAHabit]
    @Relationship var posts: [EACommunityPost]
    @Relationship var comments: [EACommunityComment]
    @Relationship var likes: [EACommunityLike]
    @Relationship var following: [EACommunityFollow]
    @Relationship var followers: [EACommunityFollow]
    @Relationship var reports: [EACommunityReport] // 超过限制！
}
```

#### 2.1 @Relationship数量控制规范（性能优化建议）

**🚨 性能建议（基于SwiftData性能特性）**：
- **建议单个模型@Relationship控制在5个以内**：基于SwiftData在to-many关系上的性能特性，建议控制关系数量
- **超过5个关系需要性能评估**：如业务需求确实需要更多关系，必须进行性能测试和评估
- **复杂模型优先拆分**：当关系过多时，优先考虑拆分为多个相关模型
- **使用关联模型扩展**：通过一对一关系连接相关模型，而非直接包含所有关系

**✅ 正确的模型设计模式**：
```swift
// ✅ 正确：核心用户模型保持简洁（5个关系）
@Model
final class EAUser {
    var id: UUID = UUID()
    var username: String
    var email: String?
    
    // 核心关系（≤5个，性能最佳）
    @Relationship(deleteRule: .cascade, inverse: \EAUserSettings.user)
    var settings: EAUserSettings?
    
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit]
    
    @Relationship(deleteRule: .cascade, inverse: \EAUserSocialProfile.user)
    var socialProfile: EAUserSocialProfile?
    
    @Relationship(deleteRule: .cascade, inverse: \EAUserModerationProfile.user)
    var moderationProfile: EAUserModerationProfile?
    
    @Relationship(deleteRule: .cascade, inverse: \EAUserDataProfile.user)
    var dataProfile: EAUserDataProfile?
}

// ✅ 正确：社交功能通过分离模型扩展（可以有更多关系）
@Model
final class EAUserSocialProfile {
    var id: UUID = UUID()
    var displayName: String?
    var bio: String?
    
    // 社交相关关系集中在此模型（可以超过5个，因为是专门的社交模型）
    @Relationship(deleteRule: .cascade, inverse: \EACommunityPost.authorSocialProfile)
    var posts: [EACommunityPost]
    
    @Relationship(deleteRule: .cascade, inverse: \EACommunityFollow.followeeProfile)
    var followers: [EACommunityFollow]
    
    @Relationship(deleteRule: .cascade, inverse: \EAFriendship.userProfile)
    var friendships: [EAFriendship]
    
    @Relationship(deleteRule: .cascade, inverse: \EAFriendRequest.receiverProfile)
    var receivedFriendRequests: [EAFriendRequest]
    
    @Relationship(deleteRule: .cascade, inverse: \EAFriendMessage.senderProfile)
    var sentMessages: [EAFriendMessage]
    
    // 与核心用户模型的关系
    var user: EAUser?
}
```

**❌ 需要避免的做法**：
```swift
// ❌ 错误：单个模型包含过多关系（>5个且无性能评估）
@Model
final class OverloadedUser {
    // 8个@Relationship关系 - 需要性能评估！
    @Relationship var settings: EAUserSettings?
    @Relationship var habits: [EAHabit]
    @Relationship var posts: [EACommunityPost]
    @Relationship var comments: [EACommunityComment]
    @Relationship var likes: [EACommunityLike]
    @Relationship var friendships: [EAFriendship]
    @Relationship var messages: [EAFriendMessage]
    @Relationship var reports: [EACommunityReport] // 需要性能评估！
}
```

#### 2.2 模型拆分指导原则

**拆分策略**：
1. **功能域拆分**：按功能模块拆分（用户核心数据 vs 社交数据 vs AI数据）
2. **访问频率拆分**：高频访问数据与低频访问数据分离
3. **数据生命周期拆分**：临时数据与持久数据分离
4. **隐私级别拆分**：敏感数据与公开数据分离
5. **性能优化拆分**：将大量关系分散到专门的模型中

### 3. 单例模式禁用与替代方案

#### 3.1 禁用单例模式

**🚫 严格禁止**：
- 禁止使用`static let shared = XXX()`模式
- 禁止全局访问的单例对象
- 禁止混合单例与依赖注入的架构
- 禁止Environment默认值形式的隐式单例

#### 3.2 依赖注入替代方案

**✅ 标准依赖注入模式**：
```swift
// ✅ 正确：通过Environment传递服务
struct ContentView: View {
    @Environment(\.sessionManager) private var sessionManager
    @Environment(\.repositoryContainer) private var repositories
    
    var body: some View {
        // 使用注入的服务
    }
}

// ✅ 正确：在App入口配置依赖
@main
struct EvolveApp: App {
    @State private var sessionManager = EASessionManager()
    @State private var repositoryContainer = EARepositoryContainer()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.sessionManager, sessionManager)
                .environment(\.repositoryContainer, repositoryContainer)
        }
    }
}

// ✅ 正确：Environment扩展（无默认值，避免隐式单例）
extension EnvironmentValues {
    @Entry var sessionManager: EASessionManager?
    @Entry var repositoryContainer: EARepositoryContainer?
}
```

### 4. AI数据桥接架构规范

#### 4.1 AI数据访问强制约束

**🔒 强制要求**：
- **AI服务不得直接访问Repository**：AI服务通过专门的数据桥接层访问数据
- **数据格式转换统一处理**：AI数据格式与业务数据格式的转换集中处理
- **AI分析结果独立存储**：AI生成的洞察和分析结果有独立的存储和缓存机制

**✅ 标准AI数据桥接架构**：
```swift
// ✅ 正确：AI数据桥接服务
@MainActor
class EAAIDataBridge: ObservableObject {
    private let repositoryContainer: EARepositoryContainer
    
    init(repositoryContainer: EARepositoryContainer) {
        self.repositoryContainer = repositoryContainer
    }
    
    // AI访问用户习惯数据的标准接口
    func getUserHabitSummary(userId: UUID) async -> EAAIUserHabitSummary {
        let habits = await repositoryContainer.habitRepository.fetchUserHabits(userId: userId)
        let completions = await repositoryContainer.habitRepository.fetchRecentCompletions(userId: userId)
        
        // 转换为AI可理解的格式
        return EAAIUserHabitSummary(
            habits: habits.map { $0.toAIFormat() },
            recentCompletions: completions.map { $0.toAIFormat() },
            analysisTimestamp: Date()
        )
    }
}

// ✅ 正确：AI洞察结果模型
@Model
final class EAAIInsight {
    var id: UUID = UUID()
    var userId: UUID
    var insightType: String
    var content: String
    var confidence: Double
    var createdAt: Date = Date()
    var expiresAt: Date
    
    // 与用户的关系
    var user: EAUser?
}
```

#### 4.2 AI缓存策略规范

**缓存层级**：
1. **用户画像缓存**：7天有效期，包含用户基本信息和偏好
2. **行为分析缓存**：3天有效期，包含习惯完成模式和趋势
3. **AI洞察缓存**：24小时有效期，包含AI生成的个性化建议
4. **相似场景缓存**：1天有效期，相似问题的AI回复

### 5. 社区功能特殊架构要求

#### 5.1 社区数据隔离原则

**🔒 强制要求**：
- **社区数据独立Repository**：社区功能必须有独立的EACommunityRepository
- **社区模型独立管理**：社区相关模型不得与核心用户模型混合
- **社区权限独立控制**：社区功能的权限控制独立于核心功能

#### 5.2 社区性能优化要求

**性能约束**：
- **分页加载强制执行**：社区内容加载必须分页，每页≤20条
- **图片懒加载**：社区图片必须实现懒加载机制
- **缓存策略**：社区内容必须有合理的本地缓存策略

### ✅ 架构一致性检查清单（基于渐进式优化更新）

**开发完成后必须检查**：
- [ ] 是否所有数据访问都通过Repository层？
- [ ] 是否存在直接的Service+ModelContext模式？
- [ ] 单个模型的@Relationship是否≤5个？
- [ ] 是否使用了禁止的单例模式？
- [ ] AI数据访问是否通过桥接层？
- [ ] 社区功能是否有独立的Repository？
- [ ] 是否遵循了依赖注入模式？

**🚨 基于渐进式优化的新增检查项**：
- [ ] **关键**：是否完全消除了外键模式（userId、habitId等字段）？
- [ ] **关键**：是否所有模型关系都使用标准SwiftData @Relationship？
- [ ] **关键**：EAUserSocialProfile和EAUserModerationProfile是否正确建立关系？
- [ ] 是否通过关系访问数据而非UUID查询？
- [ ] Repository是否包含所有分离模型的安全操作方法？
- [ ] 是否避免了跨模型的直接数据操作？

**🔍 外键模式违规检测**：
AI开发时如发现以下代码模式，**必须立即停止并重新设计**：
```swift
// ❌ 违规模式检测
var userId: UUID?        // 外键字段
var socialProfileId: UUID?  // 外键字段
let profile = await fetchSocialProfile(by: userId)  // 外键查询
```

**⚠️ 违反架构规范的后果**：
- 代码审查不通过
- 必须重构违规代码
- 可能导致性能问题和维护困难
- **新增**：违反关系模式设计将导致数据一致性问题

## 五、UI与视觉效果规范

### 1. 设计系统实现

严格遵循《UI设计规范文档》，实现以下关键要素：

- **色彩系统**：所有颜色定义在`Evolve/Assets.xcassets`的Color Set中，使用语义化命名。
- **排版系统**：使用系统字体，定义清晰的文字层级。
- **间距系统**：使用基础间距单位(8pt)的倍数定义间距。
- **组件库**：实现规范中定义的所有UI组件，保持一致的视觉风格。

### 2. 视觉表现原则

- **生态隐喻**：UI元素应体现"生态"和"能量流转"的核心隐喻。
- **动效原则**：动效应自然、流畅、有意义，不过度使用。
- **状态反馈**：各交互元素应有清晰的状态反馈（普通、按下、禁用等）。
- **一致性**：保持全应用的视觉一致性，包括颜色、字体、间距和动效。

### 3. 自适应布局

- **安全区域**：严格遵守安全区域，特别是刘海屏和Home Indicator区域。
- **设备适配**：支持所有当前iPhone尺寸，包括最小的iPhone SE。
- **方向适配**：支持竖屏模式；如需支持横屏，需特别优化布局。
- **动态字体**：响应系统字体大小设置变化。

### 4. 无障碍支持

- **VoiceOver**：为所有交互元素提供合适的标签和提示。
- **动态字体**：支持系统动态字体大小调整。
- **对比度**：保持足够的文本对比度，遵循WCAG 2.1 AA标准。
- **减少动画**：响应系统"减少动画"设置。

## 五、开发流程与质量保证

### 1. 组件开发流程

- **创建新组件**：
  - 先检查UIComponents目录，避免重复开发
  - 遵循EA命名前缀规范
  - 必须包含SwiftUI预览
  - 更新README.md记录新组件

### 2. 错误处理标准

- **错误分类**：网络错误、数据错误、业务错误
- **用户体验**：友好的错误提示和恢复建议
- **降级策略**：缓存数据、离线模式

### 3. 内存管理要求

- **循环引用防范**：正确使用[weak self]
- **资源释放**：及时取消Task和移除监听器
- **大对象管理**：图片延迟加载和适当缓存

## 六、API集成与安全规范

### 1. AI服务集成

- **接口封装**：EAAIService处理API通信，EAAIManager管理会话
- **类型安全**：使用Codable协议，定义请求/响应模型
- **错误处理**：网络、解析、AI模型错误的统一处理
- **安全存储**：API密钥使用Keychain，不硬编码

### 2. StoreKit支付集成

- **产品ID规范**：`evolve_pro_[duration]`格式
- **交易验证**：使用StoreKit 2的VerificationResult
- **安全存储**：交易信息使用Keychain存储
- **记录要求**：成功交易记录到EAPaymentRecord模型

### 3. 网络与安全

- **HTTPS强制**：所有网络请求使用HTTPS
- **网络监控**：使用NWPathMonitor监控网络状态
- **离线策略**：缓存关键数据，支持离线查看
- **重试机制**：网络错误自动重试，最多3次

## 七、测试与质量保证

### 1. SwiftUI预览规范

- **必须包含**：所有SwiftUI视图必须包含预览
- **预览数据**：使用PreviewData.swift中的模拟数据
- **多场景支持**：不同状态和设备尺寸的预览

### 2. 测试要求

- **单元测试**：所有ViewModel和Service需有单元测试
- **测试覆盖率**：核心业务逻辑≥80%
- **依赖模拟**：使用协议和依赖注入便于测试
- **UI测试**：测试核心用户流程，使用可访问性标识符

### 3. 通知系统规范

- **权限管理**：合适时机请求通知权限
- **智能调度**：基于用户设定时间和行为模式
- **个性化内容**：根据教练风格生成不同提醒内容
- **通知管理**：用户可查看和管理已安排的通知

## 八、安全与性能规范

### 1. 数据安全要求

- **敏感数据**：使用Keychain存储API密钥、支付信息
- **本地存储**：非敏感数据使用UserDefaults
- **网络安全**：强制HTTPS，实现证书验证
  - 支付相关信息必须使用Keychain存储。
- **数据传输**：
  - 网络传输使用HTTPS，实现证书固定。
  - 传输敏感数据时使用额外加密。
  - 支付数据传输遵循PCI DSS标准。
- **数据备份**：SwiftData内容包含在iOS备份中，无需额外设置。
- **数据导出安全**：
  - 导出文件使用临时目录，完成后及时清理。
  - 敏感信息在导出时适当脱敏。

### 2. 用户隐私

- **数据收集**：
  - 清晰说明收集的数据类型和用途。
  - 提供控制选项，允许用户选择退出非必要数据收集。
  - 行为分析数据匿名化处理。
- **隐私策略**：
  - 提供简明的隐私政策。
  - 符合应用商店和相关法律要求。
- **数据控制**：
  - 允许用户导出或删除其数据。
  - 提供清晰的数据使用控制界面。
  - 使用EADataExportService实现GDPR合规的数据导出。

### 3. 身份验证

- **认证方式**：
  - 支持密码、生物认证(Face ID/Touch ID)和第三方身份验证。
  - 实现安全的密码重置流程。
- **会话管理**：
  - 合理的会话过期时间。
  - 检测异常登录行为。
- **敏感操作保护**：
  - 重要操作(如支付、删除账户)需额外验证。
  - 提供操作审计日志。

### 4. 支付安全增强

- **交易验证**：
  - 使用StoreKit 2的内置验证机制。
  - 实现防欺诈检测逻辑。
- **收据管理**：
  - 安全存储和传输交易收据。
  - 定期验证订阅状态。
- **退款处理**：
  - 正确处理退款和订阅取消。
  - 及时更新用户权限状态。

## 九、开发流程规范

### 1. Git工作流程

- **分支策略**：main(稳定版本)、develop(开发集成)、feature/xxx(新功能)
- **提交规范**：遵循Conventional Commits格式
- **代码审查**：功能完整性、代码质量、性能影响、安全隐患

### 2. 风险管控

- **技术风险**：SwiftData新特性、AI服务商依赖、StoreKit支付
- **性能监控**：启动时间<1.5秒、UI流畅度60fps、内存<200MB
- **兼容性**：iOS 17.0+最低支持版本

## 十、社区功能开发规范

### 1. 内容管理

- **审核机制**：关键词过滤、用户举报、AI辅助审核
- **质量保障**：鼓励真实分享、标识AI生成内容、限制营销内容

### 2. 交互设计

- **用户体验**：保持"生态隐喻"设计一致性，体现"能量流转"概念
- **AI集成**：AI作为"社区引导者"，提供个性化鼓励和分享建议

### 3. 隐私安全

- **隐私保护**：可选分享范围、匿名选项、用户数据控制
- **数据安全**：社区数据分离存储、遵循GDPR法规

### 4. 性能优化

- **加载性能**：分页加载(≤20条)、图片懒加载、本地缓存
- **用户体验**：页面加载<2秒、离线浏览、平滑滚动

## 十一、README.md 更新规范

### 1. 更新时机

AI完成以下开发任务后必须更新README.md：
- **新功能模块**：完成PRD中定义的功能模块
- **新组件创建**：UI组件、核心服务、ViewModel
- **数据模型变更**：新增或修改@Model实体
- **API集成**：新的第三方API或支付功能
- **架构重构**：Repository重构、模型拆分、依赖注入改造等
- **规范文档更新**：开发规范文档或开发步骤文档的重要更新
- **🚨 新增**：SwiftData关系模式调整或外键模式消除工作
- **架构重构完成**：基于渐进式优化方案的架构调整完成时

### 2. 更新内容

- **日期和版本**：变更发生的日期和版本号
- **变更类型**：新增、修改、修复、移除
- **变更描述**：功能点、组件路径、数据模型变更
- **简要说明**：复杂变更的设计思路

## 技术栈
(引用技术架构文档核心技术栈)



## 项目结构概览
(引用技术架构文档项目目录结构)

## 十二、iOS开发常见崩溃与错误预防规范

### 🚨 严重错误预防清单

**核心预防要求**：
- **严禁保留print()语句**：所有调试代码必须在提交前清理
- **SwiftData关系正确定义**：遵循单端inverse规则，避免重复关系
- **@MainActor线程安全**：所有ViewModel标记@MainActor，UI更新在主线程
- **内存管理**：使用[weak self]防止循环引用，delegate声明为weak
- **iOS 17.0+兼容性**：使用现代SwiftUI语法，避免弃用API
- **错误处理**：网络请求实现超时重试，支付流程正确处理异常

### 📋 开发完成检查清单

- [ ] 清理所有print()调试语句
- [ ] 所有闭包使用[weak self]
- [ ] ViewModel标记@MainActor
- [ ] SwiftData关系正确定义
- [ ] 网络请求有错误处理
- [ ] 资源在deinit中清理

## 十三、编译错误系统性修复规范

### 🔄 禁止循环修复原则

**严禁单点修复模式**：AI在遇到编译错误时，必须采用系统性分析和一次性修复策略，避免"修复A导致B错误，修复B导致C错误"的无限循环。

### 📋 错误修复标准流程

**系统性修复原则**：
- **全面收集**：收集所有编译错误和警告，分析关联性
- **根因分析**：SwiftData关系错误（最高优先级）→ @MainActor冲突 → 重复定义 → 依赖缺失
- **批量修复**：一次性修复所有相关文件，而非逐个修复
- **优先级排序**：SwiftData关系问题 → 基础依赖 → 业务逻辑

### 🚨 SwiftData关系错误专项处理

**核心原则：遵循单端inverse规则，绝对不能删除inverse参数来"解决"编译错误**

**单端inverse规则说明**：
- **只在一端使用@Relationship(inverse:)**：双向关系只在一端定义@Relationship(inverse:)
- **另一端使用普通属性**：关系的另一端使用普通var属性，SwiftData会自动维护关系
- **避免双端@Relationship**：两端同时使用@Relationship会导致冲突和循环引用

**1. "circular reference resolving attached macro 'Relationship'"错误**

**根本原因**：
- 违反单端inverse规则（两端都使用@Relationship）
- inverse参数的KeyPath路径错误
- 关系两端类型不匹配
- 属性名拼写错误

**解决步骤**：
1. **检查单端inverse规则**：只在一端使用@Relationship(inverse:)
2. **验证KeyPath**：检查inverse参数指向正确属性
3. **类型匹配**：确认关系两端类型匹配
4. **属性名检查**：验证大小写、单复数一致

**常见错误修正示例**：
```swift
// ❌ 错误：两端都使用@Relationship（违反单端inverse规则）
@Model class EAHabit {
    @Relationship(inverse: \EAUser.habits)
    var user: EAUser?
}
@Model class EAUser {
    @Relationship(inverse: \EAHabit.user) // 错误：冗余定义
    var habits: [EAHabit] = []
}

// ✅ 正确：单端inverse规则
@Model class EAUser {
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit] = []
}
@Model class EAHabit {
    var user: EAUser? // 普通属性，无需@Relationship
}

// ❌ 错误：KeyPath指向不存在的属性
@Relationship(inverse: \EAHabit.completion) // 属性名错误
var habit: EAHabit?

// ✅ 正确：KeyPath指向正确的属性
@Relationship(inverse: \EAHabit.completions) // 属性名正确
var habit: EAHabit?
```

**2. "Cannot find type 'XXX' in scope"错误**

**系统性解决方案**：
1. **模块导入检查**：确保所有模型类都在同一模块中或正确导入
2. **类名拼写验证**：检查类名拼写是否正确，包括EA前缀
3. **@Model标记确认**：确认模型类已正确标记@Model
4. **文件名一致性**：验证文件名与类名是否一致

**3. 关系不一致错误**

**系统性解决方案**：
1. **可选性匹配**：确保关系两端的可选性匹配业务逻辑
2. **删除规则验证**：验证deleteRule设置合理（.cascade, .noAction等）
3. **关系基数检查**：检查关系的基数是否正确（一对一、一对多、多对多）
4. **集合初始化**：确认集合类型关系已初始化为空数组

### 🚨 常见循环修复陷阱及解决方案

**1. MainActor循环问题**
- **错误模式**：在Preview中使用EASessionManager.shared导致MainActor错误
- **正确方案**：一次性标记所有相关ViewModel为@MainActor，并统一处理Preview中的依赖注入

**2. 重复定义循环**
- **错误模式**：发现ReminderStyle重复定义，只删除一个文件中的定义
- **正确方案**：全局搜索所有重复定义，统一保留最完整的版本，删除其他重复项

**3. 依赖导入循环**
- **错误模式**：缺少import时只在当前文件添加
- **正确方案**：检查所有相关文件的import语句，统一补充缺失的依赖

**4. SwiftData关系循环**
- **错误模式**：遇到关系错误时删除inverse参数或改为单向关系，或者违反单端inverse规则在两端都使用@Relationship
- **正确方案**：严格遵循单端inverse规则，通过修正KeyPath路径、属性名等方式解决，**坚持完整双向关系原则但只在一端使用@Relationship**

### ⚡ 修复执行要求

**修复前必须声明**：
- 明确说明发现的所有错误类型和数量
- 说明修复计划和涉及的文件范围
- 预告可能的连锁修改
- **特别标注SwiftData关系相关的修复策略**

**修复时必须遵循**：
- 同一类型错误必须一次性全部修复
- 相关文件必须同时修改，确保一致性
- 遵循项目既有的命名规范和架构模式
- **绝对不能删除inverse参数来"解决"SwiftData关系错误**

**修复后必须验证**：
- 执行完整编译验证所有错误已解决
- 确认没有引入新的编译错误
- 检查修复是否符合项目开发规范
- **特别验证SwiftData关系功能是否正常**

### 🛑 循环修复熔断机制

**触发条件**：如果同一类型错误修复超过2次仍未解决，必须：
1. 停止继续修复
2. 全面分析项目结构和依赖关系
3. 考虑采用彻底重建策略（特别是SwiftData模型）
4. 制定完整的重构方案
5. 向用户说明情况并请求指导

**🚨 SwiftData特殊熔断机制**：
- 如果SwiftData关系错误反复出现，考虑采用"彻底清理+逐个重建"策略
- 删除所有@Model文件和SwiftData物理存储
- 按照简化架构逐个重建：EAUser → EAUserSettings → EAHabit → EACompletion
- 每个模型创建后立即验证编译和关系功能

**⚠️ 重要提醒**：修复编译错误时，质量比速度更重要。宁可花时间做全面分析，也不要陷入无限循环修复。**对于SwiftData关系错误，坚持完整双向inverse关系原则是绝对不可妥协的底线。**

- **⚠️ 重要提醒**：
  - **权威资料参考**：碰到难以修复或多次修复无法解决的SwiftData问题时，请搜索全网和苹果官方开发者相关社区，寻找权威的资料
  - **Apple官方文档优先**：始终以Apple WWDC视频、官方文档和开发者论坛为准
  - **社区最佳实践**：参考Stack Overflow、GitHub上的成熟开源项目实现

### 🔒 循环修复防范强化措施（基于问题反馈）

**预防性检查清单**：
- [ ] 修复前是否进行了全面的错误收集和分析？
- [ ] 是否识别了所有相关联的文件和依赖？
- [ ] 是否制定了批量修复计划而非逐个修复？
- [ ] 是否考虑了修复的连锁影响？

**强制执行规则**：
1. **3次规则**：同一类型错误修复超过3次必须停止，重新分析
2. **批量原则**：相关文件必须同时修改，禁止分批修复
3. **验证原则**：每次修复后必须完整编译验证
4. **文档原则**：复杂修复必须记录修复逻辑和影响范围

**紧急处理机制**：
- **立即停止**：发现循环修复趋势时立即停止
- **全面分析**：重新审查项目架构和依赖关系
- **寻求指导**：向用户说明情况并请求具体指导
- **备选方案**：准备重构或重建的备选方案

## 十四、SwiftData版本兼容性开发规范（2025年修正版）

### 🎯 SwiftData关系完整性开发要求（Apple官方标准）

**强制要求（遵循Apple WWDC 2024-2025官方指导）：**
- **单端inverse规则**：双向关系只在一端使用`@Relationship(inverse:)`，另一端用普通属性
- **关系完整性**：确保inverse参数正确指向对方模型的对应属性
- **级联删除规则**：合理使用deleteRule确保数据完整性
- **关系一致性**：通过完整的关系定义确保数据一致性
- **可选性匹配**：关系的可选性根据业务逻辑合理设置

**开发检查清单：**
```swift
// ✅ 正确：完整的双向关系设计（Apple官方标准）
@Model final class EAHabit {
    var id: UUID = UUID()
    var name: String
    var creationDate: Date = Date()
    var iconName: String
    var targetFrequency: Int
    var timeOfDay: String?
    var isActive: Bool = true
    
    // ✅ 正确：单端inverse规则 - 只在一端使用@Relationship
    @Relationship(deleteRule: .cascade, inverse: \EACompletion.habit) 
    var completions: [EACompletion] = []
    
    // 普通属性，SwiftData自动维护关系
    var user: EAUser?
    
    init(name: String, iconName: String, targetFrequency: Int, timeOfDay: String? = nil) {
        self.name = name
        self.iconName = iconName
        self.targetFrequency = targetFrequency
        self.timeOfDay = timeOfDay
    }
}

@Model final class EACompletion {
    var id: UUID = UUID()
    var date: Date = Date()
    var completionNote: String?
    var energyLevel: Int = 5 // 1-10
    
    // ✅ 正确：使用普通属性，SwiftData自动维护inverse关系
    var habit: EAHabit?
    
    init(completionNote: String? = nil, energyLevel: Int = 5) {
        self.completionNote = completionNote
        self.energyLevel = energyLevel
    }
}
```

### 🔧 SwiftData性能优化开发规范

**查询性能规范**：
- **简单@Query**：仅用于简单谓词，避免复杂逻辑
- **复杂查询**：使用@ModelActor处理复杂数据操作
- **批量操作**：使用ModelContext.transaction提高性能
- **内存管理**：及时释放大型查询结果

### 🚨 SwiftData编译错误系统性解决方案

#### 常见编译错误及解决方案

**1. "circular reference resolving attached macro 'Relationship'"**

**原因分析**：
- inverse参数引用的属性名不存在或拼写错误
- 关系两端的类型不匹配
- 模型定义顺序问题
- KeyPath路径错误

**解决步骤**：
1. 检查inverse参数的KeyPath是否正确：`\TargetModel.propertyName`
2. 确认关系两端的类型完全匹配
3. 验证属性名拼写是否正确
4. 确保模型类都已正确定义

**示例修正**：
```swift
// ❌ 错误：KeyPath不正确
@Relationship(inverse: \EAHabit.completion) // 属性名错误
var habit: EAHabit?

// ✅ 正确：KeyPath正确
@Relationship(inverse: \EAHabit.completions) // 属性名正确
var habit: EAHabit?
```

**2. "Cannot find type 'XXX' in scope"**

**解决方案**：
1. 确保所有模型类都在同一模块中或正确导入
2. 检查类名拼写是否正确
3. 确认模型类已正确标记@Model

**3. 关系不一致错误**

**解决方案**：
1. 确保关系两端的可选性匹配业务逻辑
2. 验证deleteRule设置合理
3. 检查关系的基数是否正确（一对一、一对多、多对多）

### 📊 数据库重置和迁移策略

**开发阶段**：模型变更后删除数据库文件，重新创建干净环境
**生产环境**：使用版本化迁移，支持CloudKit同步

### 💡 SwiftData最佳实践

**核心实践要求**：
1. **始终使用单端inverse规则定义关系**
2. **通过关系访问数据，而非外键查询**
3. **合理设置deleteRule确保数据完整性**
4. **使用@ModelActor处理复杂数据操作**
5. **关系赋值上下文一致性（iOS 18+强制要求）**：禁止跨Context关系赋值

**⚠️ 重要提醒**：
- 每个关系都必须有对应的inverse
- 所有关系对都必须完整定义
- 删除规则必须合理设置
- **全局单例/SessionManager只存ID，不直接持有模型对象引用**

#### 🔒 SwiftData安全赋值工具方法规范（iOS 18+关键要求）

**安全赋值核心原则**：
- **上下文一致性**：确保关系赋值的两端对象属于同一ModelContext
- **防跨Context崩溃**：避免iOS 18+环境下的Context不一致导致的崩溃
- **Repository层安全**：在数据访问层实现安全检查机制

**标准安全赋值方法模式**：
```swift
// ✅ 安全赋值方法模板
extension EAUser {
    @MainActor
    func safelyAddHabit(_ habit: EAHabit, in context: ModelContext) throws {
        // 1. 上下文一致性检查
        guard habit.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        // 2. 执行关系赋值
        self.habits.append(habit)
        habit.user = self
        // 3. 保存变更
        try context.save()
    }
}

// ✅ Repository层安全模式
@ModelActor
actor EADataRepository {
    func safelyCreateHabit(for userId: UUID, name: String) async throws -> EAHabit {
        // 1. 在当前Context中获取用户
        let user = try await fetchUser(by: userId)
        // 2. 创建并插入对象
        let habit = EAHabit(name: name, ...)
        modelContext.insert(habit)
        // 3. 建立关系
        habit.user = user
        try modelContext.save()
        return habit
    }
}
```

**关键实施要求**：
- **安全赋值方法命名**：`safelyAssign[Object]`、`safelyAdd[Object]`、`safelyCreate[Object]`
- **上下文检查**：所有关系赋值前检查ModelContext一致性
- **错误处理**：使用现有DataModelError类型（.contextMismatch等）
- **SessionManager改进**：避免直接持有SwiftData模型对象，优先通过ID重新fetch

**核心检查清单**：
- [ ] 所有关系赋值操作使用安全赋值方法
- [ ] SessionManager不直接持有SwiftData模型对象
- [ ] 遵循"插入→赋值→保存"的关系赋值顺序
- [ ] 实现适当的错误处理

**⚠️ 关键警告**：
- **绝对禁止**：直接使用SessionManager等全局对象进行关系赋值
- **强制要求**：iOS 18+环境下，违反上下文一致性将导致应用崩溃
- **🚨 新增警告**：禁止在优化后的架构中重新引入外键模式
- **🚨 新增警告**：分离模型（EAUserSocialProfile/EAUserModerationProfile）操作必须通过Repository

## 十五、AI开发质量保障机制（基于问题反馈总结）

### 🎯 质量保障核心原则

**防范机制**：
- **分层审查**：架构基础→服务层→视图层的系统性审查
- **循环防范**：3次规则+批量修复+强制验证
- **工具优化**：并行调用+重点优先+模式检测

### 📋 AI开发标准流程

**开发前检查**：
1. 是否理解了完整的需求和约束？
2. 是否制定了系统性的开发计划？
3. 是否考虑了架构一致性要求？

**开发中监控**：
1. 是否遵循了Repository模式和依赖注入？
2. 是否避免了单例模式和外键模式？
3. 是否保持了SwiftData关系的正确性？

**开发后验证**：
1. 是否进行了分层架构合规检查？
2. 是否通过了编译和功能测试？
3. 是否更新了相关文档？

### 🚨 质量红线（绝对不可违反）

1. **架构红线**：禁止外键模式、禁止单例模式、禁止直接ModelContext
2. **修复红线**：禁止循环修复、禁止单点修复、禁止跳过验证
3. **审查红线**：禁止分批发现、禁止跳过层级、禁止忽略优先级

### ⚡ 应急处理机制

**触发条件**：
- 连续3次修复同一问题
- 发现严重架构违规
- 工具调用接近限制

**处理步骤**：
1. 立即停止当前操作
2. 全面分析问题根因
3. 制定系统性解决方案
4. 向用户请求指导确认

**⚠️ 最终保障**：当AI无法确保质量时，必须主动向用户说明情况并请求人工介入。

## SwiftData关系定义口诀（务必牢记！）
> **一对关系，一端inverse，另一端普通属性，SwiftData自动帮你搞定双向！**
> 
> 生活比喻：就像两个人结婚，户口本上只需要一方写"配偶是谁"，另一方自动就能查到，不用两边都写。

## 关键SwiftData关系规则（务必遵守）
- 每对关系**只在一端**用`@Relationship(inverse:)`，**另一端只用普通属性**（不用写inverse参数）。
- **绝对禁止**两端都写`@Relationship(inverse:)`，否则会报错。
- **所有关系都要成对出现**，但只需一端写@Relationship，另一端用普通属性，SwiftData会自动维护双向关系。
- **禁止外键字段**（如userId、habitId等），只能用关系访问。
- **关系赋值顺序**：先插入Context，再赋值关系。

## 示例优化

```swift
// ✅ 正确：一对多关系，只在一端写@Relationship
@Model
class EAUser {
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit] // 只在这里写@Relationship
    // ... 其它属性 ...
    init(username: String, email: String? = nil) {
        self.username = username
        self.email = email
        self.habits = []
    }
}

@Model
class EAHabit {
    var user: EAUser? // 只用普通属性，不用@Relationship
    // ... 其它属性 ...
    init(name: String, iconName: String, targetFrequency: Int) {
        self.name = name
        self.iconName = iconName
        self.targetFrequency = targetFrequency
        self.completions = []
    }
}
```

// ... existing code ...

#### 🚫 严格禁止的外键模式（优化示例）
```swift
// ❌ 绝对禁止：使用外键字段
@Model
final class EAUserSocialProfile {
    // var userId: UUID?  // 严禁出现！
    var displayName: String?
    var user: EAUser? // 只用关系，不用userId
}

@Model  
final class EAUser {
    // var socialProfileId: UUID?  // 严禁出现！
    @Relationship(deleteRule: .cascade, inverse: \EAUserSocialProfile.user)
    var socialProfile: EAUserSocialProfile?
}
```

// ... existing code ...

#### 📝 EAAIInsight模型优化（去除外键字段）
```swift
@Model
final class EAAIInsight {
    var id: UUID = UUID()
    // var userId: UUID // ❌ 禁止外键字段
    var insightType: String
    var content: String
    var confidence: Double
    var createdAt: Date = Date()
    var expiresAt: Date
    // 与用户的关系
    var user: EAUser? // 只用关系
}
```

// ... existing code ...

### Repository模式强制要求优化
- **所有Repository都必须用@ModelActor修饰，保证线程安全。**
- 示例：
```swift
@ModelActor
actor EACommunityRepository: EACommunityRepositoryProtocol {
    // ...
}
```

// ... existing code ...

### 其它表述优化
- "所有关系都要有inverse" → "每对关系只需一端用@Relationship(inverse:)，另一端用普通属性，SwiftData自动维护双向关系。"
- "所有关系都要完整定义" → "每对关系只需一端用@Relationship(inverse:)，另一端用普通属性。"
- 关键点都加注释，举例说明。

// ... existing code ...
