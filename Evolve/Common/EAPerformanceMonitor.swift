import Foundation
import Network
import UIKit

/// 性能监控工具 - 监控应用性能和资源使用
/// 遵循EA命名规范，提供内存、网络、图片加载等性能监控功能
@MainActor
class EAPerformanceMonitor: ObservableObject {
    
    // MARK: - 🔒 修复：移除单例模式，改为依赖注入
    // 遵循.cursorrules规范，禁止使用static let shared单例模式
    // 使用方式：通过Environment传递实例
    
    // MARK: - 发布属性
    
    @Published var memoryUsage: Double = 0.0 // MB
    @Published var networkStatus: EANetworkStatus = .unknown
    @Published var imageLoadingMetrics: EAImageLoadingMetrics = EAImageLoadingMetrics()
    @Published var isPerformanceOptimized: Bool = true
    
    // MARK: - 私有属性
    
    private let networkMonitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "com.evolve.performance.monitor")
    private var memoryTimer: Timer?
    private var imageLoadStartTimes: [String: Date] = [:]
    private var isMonitoring: Bool = false
    
    // MARK: - 🔒 修复：公开初始化方法，支持依赖注入
    
    @MainActor
    init() {
        startMonitoring()
    }
    
    deinit {
        // 🔒 修复：同步清理，避免异步任务导致的内存泄漏
        isMonitoring = false
        memoryTimer?.invalidate()
        memoryTimer = nil
        networkMonitor.cancel()
        
        // 移除通知监听
        NotificationCenter.default.removeObserver(
            self,
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
        
        #if DEBUG
        print("🔒 [Performance] 性能监控已安全释放")
        #endif
    }
    
    // MARK: - 公共方法
    
    /// 开始监控
    @MainActor
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        startMemoryMonitoring()
        startNetworkMonitoring()
        startMemoryPressureMonitoring()
        
        #if DEBUG
        print("🚀 [Performance] 性能监控已启动")
        #endif
    }
    
    /// 停止监控
    @MainActor
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        memoryTimer?.invalidate()
        memoryTimer = nil
        networkMonitor.cancel()
        
        // 移除通知监听
        NotificationCenter.default.removeObserver(
            self,
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
        
        #if DEBUG
        print("⏹️ [Performance] 性能监控已停止")
        #endif
    }
    
    /// 记录图片加载开始
    func recordImageLoadStart(for url: String) {
        imageLoadStartTimes[url] = Date()
    }
    
    /// 记录图片加载完成
    func recordImageLoadComplete(for url: String, success: Bool) {
        guard let startTime = imageLoadStartTimes[url] else { return }
        
        let loadTime = Date().timeIntervalSince(startTime)
        imageLoadStartTimes.removeValue(forKey: url)
        
        Task { @MainActor in
            if success {
                imageLoadingMetrics.successfulLoads += 1
                imageLoadingMetrics.totalLoadTime += loadTime
                imageLoadingMetrics.averageLoadTime = imageLoadingMetrics.totalLoadTime / Double(imageLoadingMetrics.successfulLoads)
            } else {
                imageLoadingMetrics.failedLoads += 1
            }
            
            // 检查性能是否需要优化
            checkPerformanceOptimization()
        }
    }
    
    /// 清理图片缓存（内存压力时调用）
    func clearImageCache() {
        URLCache.shared.removeAllCachedResponses()
        
        Task { @MainActor in
            imageLoadingMetrics.cacheClears += 1
        }
    }
    
    /// 获取性能建议
    func getPerformanceRecommendations() -> [String] {
        var recommendations: [String] = []
        
        if memoryUsage > 200 {
            recommendations.append("内存使用过高，建议清理缓存")
        }
        
        if imageLoadingMetrics.averageLoadTime > 3.0 {
            recommendations.append("图片加载较慢，建议检查网络连接")
        }
        
        if imageLoadingMetrics.failedLoads > Int(Double(imageLoadingMetrics.successfulLoads) * 0.1) {
            recommendations.append("图片加载失败率较高，建议优化图片质量")
        }
        
        if networkStatus == .cellular {
            recommendations.append("当前使用蜂窝网络，建议启用省流量模式")
        }
        
        return recommendations
    }
    
    // MARK: - 私有方法
    
    /// 开始内存监控
    private func startMemoryMonitoring() {
        memoryTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            Task { @MainActor [weak self] in
                self?.updateMemoryUsage()
            }
        }
    }
    
    /// 开始网络监控
    private func startNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            Task { @MainActor [weak self] in
                self?.updateNetworkStatus(path)
            }
        }
        networkMonitor.start(queue: monitorQueue)
    }
    
    /// 更新内存使用情况
    private func updateMemoryUsage() {
        var memoryInfo = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &memoryInfo) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            memoryUsage = Double(memoryInfo.resident_size) / 1024.0 / 1024.0 // 转换为MB
        }
    }
    
    /// 更新网络状态
    private func updateNetworkStatus(_ path: NWPath) {
        if path.status == .satisfied {
            if path.usesInterfaceType(.wifi) {
                networkStatus = .wifi
            } else if path.usesInterfaceType(.cellular) {
                networkStatus = .cellular
            } else {
                networkStatus = .other
            }
        } else {
            networkStatus = .disconnected
        }
    }
    
    /// 检查性能优化状态
    private func checkPerformanceOptimization() {
        isPerformanceOptimized = memoryUsage < 200 && 
                                imageLoadingMetrics.averageLoadTime < 3.0 &&
                                imageLoadingMetrics.failedLoads < Int(Double(imageLoadingMetrics.successfulLoads) * 0.1)
    }
    
    /// 监听内存压力警告
    func startMemoryPressureMonitoring() {
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            // 🔒 修复：使用同步调用避免异步任务循环引用
            Task { @MainActor [weak self] in
                self?.handleMemoryPressureSync()
            }
        }
    }
    
    /// 处理内存压力（同步版本）
    @MainActor
    private func handleMemoryPressureSync() {
        // 清理图片缓存
        URLCache.shared.removeAllCachedResponses()
        imageLoadingMetrics.cacheClears += 1
        
        // 通知其他组件进行内存清理
        NotificationCenter.default.post(
            name: NSNotification.Name("EAMemoryPressureWarning"),
            object: nil
        )
    }
    
    /// 处理内存压力
    @MainActor
    private func handleMemoryPressure() async {
        // 清理图片缓存
        clearImageCache()
        
        // 通知其他组件进行内存清理
        NotificationCenter.default.post(
            name: NSNotification.Name("EAMemoryPressureWarning"),
            object: nil
        )
    }
}

// MARK: - 数据模型

/// 网络状态枚举
enum EANetworkStatus {
    case wifi
    case cellular
    case other
    case disconnected
    case unknown
    
    var description: String {
        switch self {
        case .wifi: return "WiFi"
        case .cellular: return "蜂窝网络"
        case .other: return "其他网络"
        case .disconnected: return "无网络连接"
        case .unknown: return "未知"
        }
    }
    
    var isConnected: Bool {
        return self != .disconnected && self != .unknown
    }
}

/// 图片加载性能指标
struct EAImageLoadingMetrics {
    var successfulLoads: Int = 0
    var failedLoads: Int = 0
    var totalLoadTime: TimeInterval = 0.0
    var averageLoadTime: TimeInterval = 0.0
    var cacheClears: Int = 0
    
    var successRate: Double {
        let total = successfulLoads + failedLoads
        return total > 0 ? Double(successfulLoads) / Double(total) : 0.0
    }
}

// MARK: - 扩展：性能优化建议

extension EAPerformanceMonitor {
    
    /// 获取社区页面专用的性能建议
    func getCommunityPerformanceRecommendations() -> [EAPerformanceRecommendation] {
        var recommendations: [EAPerformanceRecommendation] = []
        
        // 内存使用建议
        if memoryUsage > 150 {
            recommendations.append(EAPerformanceRecommendation(
                type: .memory,
                priority: .high,
                message: "内存使用较高，建议减少同时加载的帖子数量",
                action: "启用分页加载"
            ))
        }
        
        // 网络状态建议
        if networkStatus == .cellular {
            recommendations.append(EAPerformanceRecommendation(
                type: .network,
                priority: .medium,
                message: "当前使用蜂窝网络，建议优化图片质量",
                action: "启用省流量模式"
            ))
        }
        
        // 图片加载建议
        if imageLoadingMetrics.averageLoadTime > 2.0 {
            recommendations.append(EAPerformanceRecommendation(
                type: .imageLoading,
                priority: .medium,
                message: "图片加载较慢，建议启用懒加载",
                action: "优化图片加载策略"
            ))
        }
        
        return recommendations
    }
}

// MARK: - 性能建议数据模型

struct EAPerformanceRecommendation {
    let type: RecommendationType
    let priority: Priority
    let message: String
    let action: String
    
    enum RecommendationType {
        case memory
        case network
        case imageLoading
        case general
    }
    
    enum Priority {
        case low
        case medium
        case high
        case critical
    }
} 