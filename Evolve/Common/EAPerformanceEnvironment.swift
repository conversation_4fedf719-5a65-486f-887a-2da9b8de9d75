import SwiftUI

/// 🚀 性能监控环境扩展
/// 为SwiftUI视图提供性能监控器的依赖注入支持
/// 遵循项目架构规范，避免单例模式

// MARK: - Environment Key

struct PerformanceMonitorKey: EnvironmentKey {
    static let defaultValue: EAPerformanceMonitor? = nil
}

// MARK: - Environment Values Extension

extension EnvironmentValues {
    /// 性能监控器环境值
    var performanceMonitor: EAPerformanceMonitor? {
        get { self[PerformanceMonitorKey.self] }
        set { self[PerformanceMonitorKey.self] = newValue }
    }
}

// MARK: - View Extension

extension View {
    /// 设置性能监控器环境
    /// - Parameter monitor: 性能监控器实例
    /// - Returns: 配置了性能监控器的视图
    func performanceMonitor(_ monitor: EAPerformanceMonitor) -> some View {
        environment(\.performanceMonitor, monitor)
    }
}

// MARK: - Performance Metrics Types

/// 性能建议类型
enum EAPerformanceRecommendationType {
    case memory
    case network
    case imageLoading
    case scrolling
    case battery
}

/// 性能建议优先级
enum EAPerformanceRecommendationPriority {
    case low
    case medium
    case high
    case critical
}

/// 性能建议结构
struct EAPerformanceRecommendation {
    let type: EAPerformanceRecommendationType
    let priority: EAPerformanceRecommendationPriority
    let message: String
    let action: String
    
    /// 建议的图标
    var icon: String {
        switch type {
        case .memory:
            return "memorychip"
        case .network:
            return "wifi"
        case .imageLoading:
            return "photo"
        case .scrolling:
            return "scroll"
        case .battery:
            return "battery.100"
        }
    }
    
    /// 优先级颜色
    var priorityColor: Color {
        switch priority {
        case .low:
            return .green
        case .medium:
            return .yellow
        case .high:
            return .orange
        case .critical:
            return .red
        }
    }
}

// MARK: - Network Status Extension

extension EANetworkStatus {
    /// 是否已连接网络
    var isConnected: Bool {
        switch self {
        case .wifi, .cellular:
            return true
        case .disconnected, .unknown:
            return false
        }
    }
    
    /// 网络状态描述
    var description: String {
        switch self {
        case .wifi:
            return "WiFi"
        case .cellular:
            return "蜂窝网络"
        case .disconnected:
            return "未连接"
        case .unknown:
            return "未知"
        }
    }
    
    /// 网络状态图标
    var icon: String {
        switch self {
        case .wifi:
            return "wifi"
        case .cellular:
            return "antenna.radiowaves.left.and.right"
        case .disconnected:
            return "wifi.slash"
        case .unknown:
            return "questionmark.circle"
        }
    }
}

// MARK: - Performance Monitoring View Modifier

/// 性能监控视图修饰符
struct PerformanceMonitoringModifier: ViewModifier {
    let monitor: EAPerformanceMonitor
    let viewName: String
    
    @State private var viewAppearTime: Date?
    
    func body(content: Content) -> some View {
        content
            .onAppear {
                viewAppearTime = Date()
                monitor.recordViewAppear(viewName: viewName)
            }
            .onDisappear {
                if let appearTime = viewAppearTime {
                    let viewTime = Date().timeIntervalSince(appearTime)
                    monitor.recordViewDisappear(viewName: viewName, viewTime: viewTime)
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)) { _ in
                monitor.handleMemoryWarning()
            }
    }
}

extension View {
    /// 添加性能监控
    /// - Parameters:
    ///   - monitor: 性能监控器
    ///   - viewName: 视图名称
    /// - Returns: 添加了性能监控的视图
    func performanceMonitoring(monitor: EAPerformanceMonitor, viewName: String) -> some View {
        modifier(PerformanceMonitoringModifier(monitor: monitor, viewName: viewName))
    }
}

// MARK: - Performance Monitor Extensions

extension EAPerformanceMonitor {
    /// 记录视图出现
    func recordViewAppear(viewName: String) {
        // 记录视图出现时间
        #if DEBUG
        print("🚀 [Performance] View appeared: \(viewName)")
        #endif
    }
    
    /// 记录视图消失
    func recordViewDisappear(viewName: String, viewTime: TimeInterval) {
        // 记录视图停留时间
        #if DEBUG
        print("🚀 [Performance] View disappeared: \(viewName), time: \(String(format: "%.2f", viewTime))s")
        #endif
    }
    
    /// 处理内存警告
    func handleMemoryWarning() {
        // 清理缓存，释放内存
        #if DEBUG
        print("🚀 [Performance] Memory warning received, cleaning up...")
        #endif
    }
}

// MARK: - Performance Metrics View

/// 性能指标显示视图（调试用）
struct EAPerformanceMetricsView: View {
    @Environment(\.performanceMonitor) private var performanceMonitor
    
    var body: some View {
        if let monitor = performanceMonitor {
            VStack(alignment: .leading, spacing: 8) {
                Text("性能指标")
                    .font(.headline)
                    .foregroundColor(.white)
                
                HStack {
                    Text("内存使用:")
                    Text("\(String(format: "%.1f", monitor.memoryUsage)) MB")
                        .foregroundColor(monitor.memoryUsage > 200 ? .red : .green)
                }
                
                HStack {
                    Text("网络状态:")
                    Text(monitor.networkStatus.description)
                        .foregroundColor(monitor.networkStatus.isConnected ? .green : .red)
                }
                
                HStack {
                    Text("图片加载:")
                    Text("\(monitor.imageLoadingMetrics.successfulLoads)/\(monitor.imageLoadingMetrics.totalLoads)")
                        .foregroundColor(.cyan)
                }
            }
            .padding()
            .background(Color.black.opacity(0.7))
            .cornerRadius(8)
        } else {
            Text("性能监控器未初始化")
                .foregroundColor(.red)
        }
    }
}

#if DEBUG
// MARK: - Preview

struct EAPerformanceEnvironment_Previews: PreviewProvider {
    static var previews: some View {
        let monitor = EAPerformanceMonitor()
        
        VStack(spacing: 20) {
            EAPerformanceMetricsView()
            
            Text("性能监控示例")
                .foregroundColor(.white)
        }
        .padding()
        .background(Color.hexColor("002b20"))
        .performanceMonitor(monitor)
        .performanceMonitoring(monitor: monitor, viewName: "PreviewView")
        .previewDisplayName("性能监控环境")
    }
}
#endif
