import SwiftUI
import SwiftData
import UserNotifications

@main
struct EvolveApp: App {
    
    // ✅ 修复：使用Apple官方标准的多模型ModelContainer配置
    @MainActor
    static let sharedModelContainer: ModelContainer = {
        do {
            // 🔧 【叶同学持久化优化】确保数据真正保存到磁盘
            #if DEBUG
            // 开发模式：启用真正的持久化存储，用户数据将保留
            // 强制删除已注释掉：forceDeleteAllSwiftDataFiles()
            #endif
            
            // 🔑 创建iOS 18.2+兼容的持久化配置
            let configuration = ModelConfiguration(
                isStoredInMemoryOnly: false,   // 🔑 关键：必须保存到磁盘文件
                allowsSave: true,              // 🔑 关键：允许保存操作
                groupContainer: .none,         // 使用应用默认容器
                cloudKitDatabase: .none        // 暂不启用iCloud同步
            )
            
            // ✅ Apple官方推荐：使用可变参数语法确保iOS 17.0-18.5+兼容性
            // 包含所有数据模型，确保社区功能完整支持
            let container = try ModelContainer(
                for: EAUser.self,
                EAUserSettings.self, 
                EAHabit.self,
                EACompletion.self,
                EAUserSocialProfile.self,
                EAUserModerationProfile.self,
                EAUserDataProfile.self,  // ✅ 新增：用户数据档案模型
                EAUserAuthInfo.self,  // ✅ 新增：用户认证信息模型
                EAAIMessage.self,
                EAPayment.self,
                EAAnalytics.self,
                EAContent.self,
                EAPath.self,
                EACommunityPost.self,
                EACommunityComment.self,
                EACommunityLike.self,
                EACommunityFollow.self,
                EACommunityReport.self,
                EAUniverseChallenge.self,  // ✅ 新增：宇宙挑战模型
                EAUniverseChallengeParticipation.self,  // ✅ 新增：挑战参与模型
                configurations: configuration
            )
            
            // ✅ 新增：Context一致性验证
            #if DEBUG
            let mainContext = container.mainContext
            print("✅ 共享ModelContainer创建成功，Context ID: \(ObjectIdentifier(mainContext))")
            #endif
            
            return container
        } catch {
            #if DEBUG
            fatalError("Failed to create ModelContainer: \(error)")
            #else
            // 生产环境：提供降级策略
            fatalError("Database initialization failed. Please restart the app.")
            #endif
        }
    }()
    
    // ✅ 修复：确保Repository容器使用共享Container
    @StateObject private var repositoryContainer = EARepositoryContainerImpl(modelContainer: Self.sharedModelContainer)
    
    // ✅ 修复：同步创建SessionManager，避免异步初始化问题
    @StateObject private var sessionManager = EASessionManager()
    
    // ✅ 新增：创建其他服务实例，移除单例模式
    @StateObject private var aiInAppReminderManager = EAAIInAppReminderManager()
    @StateObject private var repositoryPerformanceMonitor = EARepositoryPerformanceMonitor()
    @StateObject private var sheetManager = EASheetManager()
    @State private var notificationDelegate = NotificationDelegate()
    
    init() {
        requestNotificationPermission()
        
        // ✅ 新增：Context一致性验证
        #if DEBUG
        validateContainerConsistency()
        #endif
    }
    
    /// ✅ 新增：验证Container一致性
    #if DEBUG
    private func validateContainerConsistency() {
        let containerContext = Self.sharedModelContainer.mainContext
        print("🔍 验证共享Container一致性，Context ID: \(ObjectIdentifier(containerContext))")
    }
    #endif
    
    /// 检查现有用户或创建新用户（已移至ViewModel中处理）
    @MainActor
    static func checkOrCreateUser() async {
        // 此方法已废弃，用户检查逻辑已移至具体ViewModel中通过依赖注入处理
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.repositoryContainer, repositoryContainer)
                .environment(\.sessionManager, sessionManager)
                .environment(\.aiInAppReminderManager, aiInAppReminderManager)
                .environment(\.repositoryPerformanceMonitor, repositoryPerformanceMonitor)
                .environment(\.sheetManager, sheetManager)
                .environment(\.notificationDelegate, notificationDelegate)
                .withSheetManager(sheetManager)
                .onAppear {
                    initializeAppData()
                }
        }
        // ✅ 修复：使用预创建的ModelContainer实例
        .modelContainer(Self.sharedModelContainer)
    }
    
    /// 请求通知权限
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                #if DEBUG
                if granted {
                    // 通知权限已获取
                } else if error != nil {
                    // 通知权限请求失败
                } else {
                    // 用户拒绝了通知权限
                }
                #endif
            }
        }
    }
    
    /// 初始化应用数据
    private func initializeAppData() {
        Task { @MainActor in
            // ✅ 修复：设置SessionManager的Repository容器
            sessionManager.setRepositoryContainer(repositoryContainer)

            // 🔑 关键修复：使用增强版会话恢复方法
            await sessionManager.restoreSessionOnAppLaunch()

            #if DEBUG
            print("🚀 [AppEntry] 应用数据初始化完成")
            #endif
        }
    }
    
    // 🔧 【保留但注释】强制清理方法（仅在需要重置时手动启用）
    // 叶同学如果需要重置所有数据，可以取消下面代码的注释并重新运行
    /*
    @MainActor
    static func forceDeleteAllSwiftDataFiles() {
        let fileManager = FileManager.default
        
        // 开始彻底清理SwiftData存储
        
        // 1. 删除应用支持目录中的所有数据库文件
        let urls = fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask)
        if let appSupportURL = urls.first {
            // 删除所有可能的数据库文件
            let possibleFiles = [
                "default.store", "default.store-wal", "default.store-shm",
                "Model.sqlite", "Model.sqlite-wal", "Model.sqlite-shm",
                "DataModel.sqlite", "DataModel.sqlite-wal", "DataModel.sqlite-shm"
            ]
            
            for fileName in possibleFiles {
                let fileURL = appSupportURL.appendingPathComponent(fileName)
                if fileManager.fileExists(atPath: fileURL.path) {
                    try? fileManager.removeItem(at: fileURL)
                }
            }
        }
        
        // 清除相关设置
        UserDefaults.standard.removeObject(forKey: "hasCreatedDefaultUser")
        UserDefaults.standard.synchronize()
    }
    */
}

// MARK: - ContentView（主视图）

struct ContentView: View {
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Environment(\.sessionManager) private var sessionManager
    @StateObject private var onboardingViewModel = EAOnboardingViewModel()
    
    // ✅ 修复：不使用@StateObject，改为@State，在setupDependencies中正确初始化
    @State private var authViewModel: EAAuthViewModel?
    
    // 🚀 新增：强化UI状态响应机制
    @State private var forceRefreshToggle = false
    
    // 🔑 关键修复：添加会话恢复状态，避免登录页面闪现
    @State private var isSessionRestoring = true
    @State private var sessionRestoreCompleted = false
    
    var body: some View {
        Group {
            if !onboardingViewModel.hasCompletedOnboarding {
                // 新用户：显示引导页面
                OnboardingFlow()
                    .environmentObject(onboardingViewModel)
            } else if isSessionRestoring && !sessionRestoreCompleted {
                // 🔑 关键修复：会话恢复中，显示加载状态而非登录页面
                ProgressView("正在恢复会话...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.9))
                    .preferredColorScheme(.dark)
            } else if sessionManager.isLoggedIn {
                // 🔑 关键修复：已完成引导且已登录，直接显示主应用界面
                MainAppView()
                    .environmentObject(sessionManager)
                    .environmentObject(authViewModel ?? EAAuthViewModel())
            } else if let authViewModel = authViewModel {
                // 已完成引导但未登录：显示认证界面
                AuthenticationFlow()
                    .environmentObject(authViewModel)
                    .environmentObject(sessionManager)
            } else {
                // 初始化中
                ProgressView("正在初始化...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .onAppear {
            setupDependencies()
            startSessionRestoreTimer()
        }
        .preferredColorScheme(.dark)
        // 🚀 新增：监听SessionManager状态变化的多重机制
        .onReceive(sessionManager.$isLoggedIn) { isLoggedIn in
            // 🔑 关键修复：会话状态变化时，标记恢复完成
            if isLoggedIn || sessionRestoreCompleted {
                isSessionRestoring = false
            }
            
            // 强制触发视图重新评估
            withAnimation(.easeInOut(duration: 0.3)) {
                forceRefreshToggle.toggle()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("EASessionLogoutCompleted"))) { _ in
            // 收到退出登录完成通知，强制刷新UI
            withAnimation(.easeInOut(duration: 0.3)) {
                forceRefreshToggle.toggle()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("EASessionEmergencyReset"))) { _ in
            // 收到紧急重置通知，立即刷新UI
            forceRefreshToggle.toggle()
        }
        // 🔑 关键：使用不可见的状态变化强制触发视图重绘
        .opacity(forceRefreshToggle ? 1.0 : 1.0)
    }
    
    /// 设置依赖关系
    private func setupDependencies() {
        // ✅ 修复：使用正确的SessionManager初始化AuthViewModel
        if authViewModel == nil {
            // 使用Environment传递的SessionManager创建AuthViewModel
            let viewModel = EAAuthViewModel(authService: nil, sessionManager: sessionManager)
            
            // 设置Repository容器
            if let container = repositoryContainer {
                viewModel.setRepositoryContainer(container)
                
                // ✅ 修复：移除重复的Repository设置，避免重复调用
                // SessionManager的Repository容器已在initializeAppData中设置
            }
            
            self.authViewModel = viewModel
        }
    }
    
    /// 🔑 新增：启动会话恢复计时器，避免无限等待
    private func startSessionRestoreTimer() {
        // 最多等待2秒，如果会话恢复未完成则认为用户未登录
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            if isSessionRestoring {
                sessionRestoreCompleted = true
                isSessionRestoring = false
            }
        }
        
        // 监听会话恢复完成通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("EASessionLoginCompleted"),
            object: nil,
            queue: .main
        ) { _ in
            sessionRestoreCompleted = true
            isSessionRestoring = false
        }
        
        // 🔑 新增：监听会话恢复失败通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("EASessionRestoreFailed"),
            object: nil,
            queue: .main
        ) { _ in
            sessionRestoreCompleted = true
            isSessionRestoring = false
        }
    }
}

// MARK: - 数据库初始化视图

struct DatabaseInitializationView: View {
    @EnvironmentObject private var databaseManager: EADatabaseManager
    
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("正在初始化数据库...")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("新架构：\(databaseManager.getStateDescription())")
                .font(.caption)
                .foregroundColor(Color.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - 数据库错误视图

struct DatabaseErrorView: View {
    @EnvironmentObject private var databaseManager: EADatabaseManager
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.red)
            
            Text("数据库初始化失败")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("应用无法正常启动，请尝试重新初始化数据库")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("重新初始化") {
                databaseManager.forceReinitialize()
            }
            .buttonStyle(.borderedProminent)
            
            Button("紧急重置") {
                databaseManager.emergencyReset()
            }
            .buttonStyle(.bordered)
            .foregroundColor(.red)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
        .padding()
    }
}

// MARK: - 引导流程视图

struct OnboardingFlow: View {
    @EnvironmentObject var onboardingViewModel: EAOnboardingViewModel
    
    var body: some View {
        EAOnboardingView()
            .environmentObject(onboardingViewModel)
    }
}

// MARK: - 认证流程视图

struct AuthenticationFlow: View {
    @EnvironmentObject var authViewModel: EAAuthViewModel
    @EnvironmentObject var sessionManager: EASessionManager
    @State private var currentView: AuthView = .login
    
    enum AuthView {
        case login
        case registration
        case forgotPassword
    }
    
    var body: some View {
        NavigationStack {
            ZStack {
                // 背景
                EABackgroundView(style: .authentication, showParticles: true)
                
                // 认证视图
                switch currentView {
                case .login:
                    EALoginView()
                        .environmentObject(authViewModel)
                case .registration:
                    EARegistrationView()
                        .environmentObject(authViewModel)
                case .forgotPassword:
                    EAForgotPasswordView()
                        .environmentObject(authViewModel)
                }
            }
            .navigationBarHidden(true)
        }
        .onReceive(sessionManager.$isLoggedIn) { isLoggedIn in
            if isLoggedIn {
                // 登录成功后的处理已经在SessionManager中完成
            }
        }
    }
}

// MARK: - 主应用视图

struct MainAppView: View {
    @EnvironmentObject var sessionManager: EASessionManager
    @EnvironmentObject var authViewModel: EAAuthViewModel
    @Environment(\.repositoryContainer) private var repositoryContainer
    
    var body: some View {
        EAMainTabView()
            .environmentObject(sessionManager)
            .environmentObject(authViewModel)
            .environment(\.communityAIDataBridge, repositoryContainer?.communityAIDataBridge)
    }
}

// MARK: - 预览

#Preview("App - 新用户（引导页面）") {
    @Previewable @State var onboardingViewModel: EAOnboardingViewModel = {
        let vm = EAOnboardingViewModel()
        vm.hasCompletedOnboarding = false
        return vm
    }()
    
    let repositoryContainer = EARepositoryContainerImpl(modelContainer: PreviewData.container)
    let sessionManager = EASessionManager(repositoryContainer: repositoryContainer)
    
    ContentView()
        .modelContainer(PreviewData.container)
        .environment(\.repositoryContainer, repositoryContainer)
        .environment(\.sessionManager, sessionManager)
        .environmentObject(onboardingViewModel)
}

#Preview("App - 老用户未登录（认证页面）") {
    @Previewable @State var onboardingViewModel: EAOnboardingViewModel = {
        let vm = EAOnboardingViewModel()
        vm.hasCompletedOnboarding = true
        return vm
    }()
    
    let repositoryContainer = EARepositoryContainerImpl(modelContainer: PreviewData.container)
    let sessionManager = EASessionManager(repositoryContainer: repositoryContainer)
    
    ContentView()
        .modelContainer(PreviewData.container)
        .environment(\.repositoryContainer, repositoryContainer)
        .environment(\.sessionManager, sessionManager)
        .environmentObject(onboardingViewModel)
}

#Preview("App - 已登录（主应用）") {
    @Previewable @State var onboardingViewModel: EAOnboardingViewModel = {
        let vm = EAOnboardingViewModel()
        vm.hasCompletedOnboarding = true
        return vm
    }()
    
    let repositoryContainer = EARepositoryContainerImpl(modelContainer: PreviewData.container)
    let sessionManager = EASessionManager(repositoryContainer: repositoryContainer)
    
    ContentView()
        .modelContainer(PreviewData.container)
        .environment(\.repositoryContainer, repositoryContainer)
        .environment(\.sessionManager, sessionManager)
        .environmentObject(onboardingViewModel)
        .task {
            // 模拟登录状态用于预览
            await sessionManager.simulateLogin()
            PreviewData.initializePreviewData()
        }
}
