import SwiftUI
import SwiftData

/// 主Tab导航视图
/// 完全按照原型图设计，包含四个核心功能模块：今日、图鉴、灵境、我的
/// 使用自定义设计实现毛玻璃背景和发光效果，支持设备旋转和动态布局适配
struct EAMainTabView: View {
    @State private var selectedTab: Tab = .today
    @Environment(\.repositoryContainer) private var repositoryContainer
    @EnvironmentObject var sessionManager: EASessionManager
    
    /// Tab枚举定义
    enum Tab: String, CaseIterable {
        case today = "today"
        case atlas = "atlas"
        case auraSpace = "auraSpace"
        case community = "community"
        case me = "me"
        
        var title: String {
            switch self {
            case .today: return "今日"
            case .atlas: return "图鉴"
            case .auraSpace: return "灵境"
            case .community: return "星域"
            case .me: return "我的"
            }
        }
        
        var iconSelected: String {
            switch self {
            case .today: return "sun.max.fill"
            case .atlas: return "book.fill" 
            case .auraSpace: return "brain.head.profile"
            case .community: return "person.3.fill"
            case .me: return "person.fill"
            }
        }
        
        var iconUnselected: String {
            switch self {
            case .today: return "sun.max"
            case .atlas: return "book"
            case .auraSpace: return "brain.head.profile"
            case .community: return "person.3"
            case .me: return "person"
            }
        }
        
        var subtitle: String {
            switch self {
                          case .today: return "查看今日计划进度和能量状态"
            case .atlas: return "管理你的计划生态系统"
            case .auraSpace: return "与AI智慧核心深度交流"
            case .community: return "星际探索者信标网络，共享宇宙能量"
            case .me: return "个人中心与设置"
            }
        }
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .bottom) {
                // 主内容区域 - 完全占据整个屏幕
                Group {
                    switch selectedTab {
                    case .today:
                        EATodayView(sessionManager: sessionManager)
                            .environmentObject(sessionManager)
                    case .atlas:
                        EAAtlasView()
                    case .auraSpace:
                        EAAuraSpaceView()
                    case .community:
                        EACommunityView()
                            .environmentObject(sessionManager)
                            .environment(\.repositoryContainer, repositoryContainer)
                    case .me:
                        NavigationStack {
                            EAMeView(viewModel: EAMeViewModel(sessionManager: sessionManager))
                                .environmentObject(sessionManager)
                        }
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .ignoresSafeArea(.all) // 让内容完全占据屏幕
                
                // 自定义Tab Bar - 浮动在底部，确保完全适配屏幕宽度
                VStack(spacing: 0) {
                    // Tab Bar内容区域（固定49pt高度）- 传递精确的屏幕宽度
                    CustomTabBarContent(
                        selectedTab: $selectedTab,
                        screenWidth: geometry.size.width
                    )
                    .frame(height: 49)
                    .frame(maxWidth: .infinity) // 确保Tab Bar内容占据最大宽度
                    
                    // 底部安全区域占位 - 简化处理
                    Color.clear
                        .frame(height: 0)
                        .safeAreaInset(edge: .bottom) {
                            Color.clear.frame(height: 0)
                        }
                }
                .background(
                    // 毛玻璃背景效果 - 延伸到屏幕底部
                    ZStack {
                        // 深色半透明背景
                        Color.black.opacity(0.2)
                        
                        // 毛玻璃效果
                        Rectangle()
                            .fill(.ultraThinMaterial)
                            .environment(\.colorScheme, .dark) // 强制深色毛玻璃
                    }
                    .ignoresSafeArea(.all, edges: .bottom)
                )
                .overlay(
                    // 顶部边框线
                    Rectangle()
                        .fill(Color.white.opacity(0.1))
                        .frame(height: 1),
                    alignment: .top
                )
            }
        }
        .ignoresSafeArea(.all) // 确保GeometryReader获取完整屏幕尺寸
    }
}

/// 自定义Tab Bar内容组件 - 支持动态宽度适配和设备旋转
struct CustomTabBarContent: View {
    @Binding var selectedTab: EAMainTabView.Tab
    let screenWidth: CGFloat
    
    // 计算Tab项目的最佳宽度 - 修复双重padding问题
    private var tabItemWidth: CGFloat {
        let totalTabs = EAMainTabView.Tab.allCases.count
        let edgePadding: CGFloat = 12 // 左右各6pt边距，保持视觉美观
        let availableWidth = screenWidth - edgePadding
        return availableWidth / CGFloat(totalTabs)
    }
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(EAMainTabView.Tab.allCases, id: \.self) { tab in
                TabBarItem(
                    tab: tab,
                    isSelected: selectedTab == tab,
                    itemWidth: tabItemWidth
                ) {
                    // 点击切换Tab，添加触觉反馈和流畅动画
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7, blendDuration: 0)) {
                        selectedTab = tab
                    }
                    
                    // 增强触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.prepare() // 预准备，提升响应速度
                    impactFeedback.impactOccurred()
                }
            }
        }
        .frame(maxWidth: .infinity) // 确保HStack占据最大可用宽度
        .padding(.horizontal, 6) // 统一的水平边距，避免重复计算
    }
}

/// Tab Bar单个项目组件 - 支持动态宽度和增强的视觉效果
struct TabBarItem: View {
    let tab: EAMainTabView.Tab
    let isSelected: Bool
    let itemWidth: CGFloat
    let action: () -> Void
    
    @State private var isPressed: Bool = false
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 2) { // iOS标准图标文字间距2pt
                Spacer(minLength: 0) // 顶部弹性间距
                
                // 图标容器 - 增加按压反馈
                ZStack {
                    // 按压时的背景圆圈
                    if isPressed {
                        Circle()
                            .fill(Color.white.opacity(0.1))
                            .frame(width: 32, height: 32)
                            .transition(.scale.combined(with: .opacity))
                    }
                    
                    // 图标
                    Image(systemName: isSelected ? tab.iconSelected : tab.iconUnselected)
                        .font(.system(size: 25, weight: isSelected ? .semibold : .medium))
                        .foregroundColor(isSelected ? Color(red: 0.25, green: 0.88, blue: 0.82) : Color(red: 0.54, green: 0.54, blue: 0.56))
                        .shadow(
                            color: isSelected ? Color(red: 0.25, green: 0.88, blue: 0.82).opacity(0.6) : .clear,
                            radius: isSelected ? 6 : 0,
                            x: 0,
                            y: 0
                        )
                        .scaleEffect(isSelected ? 1.05 : (isPressed ? 0.95 : 1.0))
                }
                
                // 文字标签
                Text(tab.title)
                    .font(.system(size: 10, weight: isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? Color(red: 0.25, green: 0.88, blue: 0.82) : Color(red: 0.54, green: 0.54, blue: 0.56))
                    .shadow(
                        color: isSelected ? Color(red: 0.25, green: 0.88, blue: 0.82).opacity(0.4) : .clear,
                        radius: isSelected ? 3 : 0,
                        x: 0,
                        y: 0
                    )
                    .lineLimit(1)
                    .minimumScaleFactor(0.8) // 确保在较窄屏幕上文字不会被截断
                
                Spacer(minLength: 0) // 底部弹性间距
            }
            .frame(width: itemWidth, height: 49) // 使用精确的分配宽度，移除最小宽度限制
            .contentShape(Rectangle()) // 扩大点击区域
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0) // 整体按压效果
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
        .animation(.spring(response: 0.3, dampingFraction: 0.7, blendDuration: 0), value: isSelected)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .accessibilityLabel(tab.title)
        .accessibilityHint(tab.subtitle)
        .accessibilityAddTraits(isSelected ? [.isSelected] : [])
    }
}

#Preview("主Tab导航") {
    EAMainTabView()
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.dark)
        .onAppear {
            PreviewData.initializePreviewData()
        }
}

#Preview("主Tab导航 - 浅色模式") {
    EAMainTabView()
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.light)
}

#Preview("主Tab导航 - 横屏模式") {
    EAMainTabView()
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.dark)
        .onAppear {
            PreviewData.initializePreviewData()
        }
}

#Preview("主Tab导航 - iPhone SE") {
    EAMainTabView()
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.dark)
        .onAppear {
            PreviewData.initializePreviewData()
        }
}

#Preview("主Tab导航 - 大屏设备") {
    EAMainTabView()
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.dark)
        .onAppear {
            PreviewData.initializePreviewData()
        }
} 