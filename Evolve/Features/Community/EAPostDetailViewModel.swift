import SwiftUI
import SwiftData

/// 帖子详情页面ViewModel
/// 处理评论加载、点赞交互、评论提交和帖子操作
@MainActor
class EAPostDetailViewModel: ObservableObject {
    
    // MARK: - Properties
    
    /// 帖子数据
    let post: EACommunityPost
    
    /// Repository容器（强制依赖注入）
    private let repositoryContainer: EARepositoryContainer

    /// ✅ 修复：添加sessionManager依赖注入（可变，支持运行时设置）
    private var sessionManager: EASessionManager
    
    /// 评论列表
    @Published var comments: [EACommunityComment] = []
    
    /// 评论输入文本
    @Published var commentText: String = ""
    
    /// 回复目标评论
    @Published var replyTargetComment: EACommunityComment?
    
    /// UI状态
    @Published var isLoadingComments: Bool = false
    @Published var isSubmittingComment: Bool = false
    @Published var showAlert: Bool = false
    @Published var alertMessage: String?
    @Published var showDeleteDialog: Bool = false
    @Published var shouldDismiss: Bool = false  // 🔑 新增：控制页面关闭
    
    /// 错误恢复状态
    @Published var loadingError: String?
    @Published var canRetry: Bool = false
    
    /// 评论排序方式
    @Published var commentSortOrder: CommentSortOrder = .newest
    
    /// 当前用户缓存
    private var currentUser: EAUser?
    
    // MARK: - Initialization
    
    /// 删除成功回调
    var onPostDeleted: (() -> Void)?

    /// ✅ 修复：强制Repository模式，移除ModelContext依赖
    init(post: EACommunityPost, sessionManager: EASessionManager, repositoryContainer: EARepositoryContainer, onPostDeleted: (() -> Void)? = nil) {
        self.post = post
        self.sessionManager = sessionManager
        self.repositoryContainer = repositoryContainer
        self.onPostDeleted = onPostDeleted
    }

    
    // MARK: - Public Methods
    
    /// 加载评论列表 - 强制Repository模式
    func loadComments() {
        isLoadingComments = true
        loadCommentsWithRepository(repositoryContainer)

        // 🔑 新增：同时刷新用户状态，确保UI正确显示
        refreshUserState()
    }

    /// 🔑 新增：刷新用户状态
    func refreshUserState() {
        Task { @MainActor in
            do {
                // 尝试获取当前用户，这会触发会话恢复
                let _ = try await getCurrentUser()

                // 触发UI更新
                objectWillChange.send()

                #if DEBUG
                #endif
            } catch {
                #if DEBUG
                #endif
            }
        }
    }

    /// 🔑 新增：重试加载评论
    func retryLoadComments() {
        loadingError = nil
        canRetry = false
        loadComments()
    }
    
    /// 使用Repository加载评论
    private func loadCommentsWithRepository(_ container: EARepositoryContainer) {
        Task { @MainActor in
            do {
                let fetchedComments = try await container.communityRepository.fetchComments(for: post.id)
                self.comments = fetchedComments
                self.isLoadingComments = false
                self.sortComments()
            } catch {
                self.handleLoadingError("加载评论失败：\(error.localizedDescription)")
            }
        }
    }
    


    
    /// 提交评论
    func submitComment() async {
        guard canSubmitComment else { return }

        // 🔑 修复：设置提交状态，防止重复提交
        await MainActor.run {
            isSubmittingComment = true
        }

        await performCommentSubmission()
    }
    
    /// 执行评论提交的核心逻辑（增强版）
    private func performCommentSubmission() async {
        do {
            // 🔑 第一步：获取当前用户（使用增强版认证检查）
            let currentUser = try await getCurrentUser()

            // 🔑 第二步：验证评论内容
            let trimmedContent = commentText.trimmingCharacters(in: .whitespacesAndNewlines)
            guard !trimmedContent.isEmpty else {
                throw NSError(domain: "CommunityError", code: 2, userInfo: [NSLocalizedDescriptionKey: "评论内容不能为空"])
            }

            guard trimmedContent.count <= 500 else {
                throw NSError(domain: "CommunityError", code: 3, userInfo: [NSLocalizedDescriptionKey: "评论内容不能超过500字"])
            }

            // 🔑 第三步：验证帖子状态
            guard post.isVisible else {
                throw NSError(domain: "CommunityError", code: 4, userInfo: [NSLocalizedDescriptionKey: "该帖子已不可用"])
            }

            #if DEBUG
            #endif

            // 🔑 第四步：通过Repository创建评论
            let newComment = try await repositoryContainer.communityRepository.createComment(
                content: trimmedContent,
                authorId: currentUser.id,
                postId: post.id,
                parentCommentId: replyTargetComment?.id
            )

            // 🔑 第五步：更新UI状态
            await updateUIAfterSubmission(comment: newComment)

            #if DEBUG
            #endif

        } catch {
            await MainActor.run {
                self.handleSubmissionError(error)
            }
        }
    }
    
    // 🔑 修复：移除不再需要的辅助方法，简化代码结构
    
    /// 更新UI状态
    private func updateUIAfterSubmission(comment: EACommunityComment) async {
        await MainActor.run {
            // 添加到本地列表顶部
            self.comments.insert(comment, at: 0)

            // 清空输入框和回复状态
            self.commentText = ""
            self.replyTargetComment = nil
            self.isSubmittingComment = false
        }
    }
    
    /// 处理提交错误（增强版错误处理）
    private func handleSubmissionError(_ error: Error) {
        isSubmittingComment = false

        // 🔑 改进：根据错误类型提供更详细的提示
        if let nsError = error as NSError? {
            if nsError.domain == "CommunityError" && nsError.code == 1 {
                showError("登录状态已过期，请重新登录后再试")
            } else if nsError.localizedDescription.contains("网络") {
                showError("网络连接异常，请检查网络后重试")
            } else if nsError.localizedDescription.contains("内容") {
                showError("评论内容不符合要求，请修改后重试")
            } else if error is CommunityError {
                showError("评论提交失败：\(error.localizedDescription)")
            } else {
                showError("评论提交失败，请稍后重试")
            }
        } else {
            showError("评论提交失败，请稍后重试")
        }

        #if DEBUG
        #endif
    }
    
    /// 切换帖子点赞状态
    func togglePostLike() {
        // 此功能委托给EACommunityViewModel处理
        // 这里只是占位，实际集成时需要与主社区ViewModel协调
    }
    
    /// 切换评论点赞状态 - 优化性能
    func toggleCommentLike(_ comment: EACommunityComment) {
        Task { @MainActor in
            await performCommentLikeToggle(comment)
        }
    }
    
    /// 执行评论点赞切换操作 - 拆分复杂逻辑
    private func performCommentLikeToggle(_ comment: EACommunityComment) async {
        do {
            let currentUser = try await getCurrentUser()
            let existingLike = try await findExistingLike(for: comment, user: currentUser)
            
            if let existingLike = existingLike {
                await updateExistingLike(existingLike, for: comment)
            } else {
                await createNewLike(for: comment, user: currentUser)
            }
            
            // Repository模式下，保存操作已在具体方法中完成
            
        } catch {
            showError("点赞操作失败：\(error.localizedDescription)")
        }
    }
    
    /// 查找现有点赞 - 通过Repository
    private func findExistingLike(for comment: EACommunityComment, user: EAUser) async throws -> EACommunityLike? {
        // 通过Repository查找现有点赞
        return try await repositoryContainer.communityRepository.findExistingLike(
            commentId: comment.id,
            userId: user.id
        )
    }
    
    /// 更新现有点赞状态
    private func updateExistingLike(_ existingLike: EACommunityLike, for comment: EACommunityComment) async {
        if existingLike.isActive {
            existingLike.isActive = false
            // 点赞数量通过计算关系获得，无需手动维护计数
        } else {
            existingLike.isActive = true
            // 点赞数量通过计算关系获得，无需手动维护计数
        }
        existingLike.creationDate = Date()
    }
    
    /// 删除现有点赞 - 通过Repository
    private func removeExistingLike(_ existingLike: EACommunityLike, comment: EACommunityComment) async {
        do {
            try await repositoryContainer.communityRepository.deleteLike(existingLike.id)
        } catch {
            await MainActor.run {
                self.showError("删除点赞失败：\(error.localizedDescription)")
            }
        }
    }
    
    /// 重新激活点赞
    private func reactivateLike(_ existingLike: EACommunityLike, comment: EACommunityComment) async {
        existingLike.isActive = true
        // 点赞数量通过计算关系获得，无需手动维护计数
        existingLike.creationDate = Date()
    }
    
    /// 创建新点赞 - 通过Repository
    private func createNewLike(for comment: EACommunityComment, user: EAUser) async {
        do {
            _ = try await repositoryContainer.communityRepository.createLike(
                targetType: "comment",
                targetCommentId: comment.id,
                userId: user.id,
                userEnergyLevel: 5
            )
        } catch {
            await MainActor.run {
                self.showError("创建点赞失败：\(error.localizedDescription)")
            }
        }
    }
    
    /// 准备回复评论
    func prepareReplyToComment(_ comment: EACommunityComment) {
        replyTargetComment = comment
    }
    
    /// 取消回复
    func cancelReply() {
        replyTargetComment = nil
    }
    
    /// 切换评论排序
    func toggleCommentSort() {
        switch commentSortOrder {
        case .newest:
            commentSortOrder = .oldest
        case .oldest:
            commentSortOrder = .mostLiked
        case .mostLiked:
            commentSortOrder = .newest
        }
        sortComments()
    }
    
    /// 分享帖子
    func sharePost() {
        // TODO: 实现分享功能
        showAlert(message: "分享功能开发中...")
    }
    
    /// 举报帖子
    func reportPost() {
        // TODO: 实现举报功能
        showAlert(message: "举报功能开发中...")
    }
    
    /// 显示用户资料
    func showUserProfile(for user: EAUser? = nil) {
        // TODO: 实现用户资料页面
        let username = user?.username ?? post.getAuthorUsername()
        showAlert(message: "查看 \(username) 的个人资料")
    }
    
    /// 显示删除确认
    func showDeleteConfirmation() {
        showDeleteDialog = true
    }
    
    /// 删除帖子 - 通过Repository（增强版错误处理）
    func deletePost() {
        Task {
            do {
                // 🔑 第一步：获取当前用户（使用增强版认证检查）
                let currentUser = try await getCurrentUser()

                // 🔑 第二步：详细权限检查
                guard let postAuthor = self.post.getAuthor() else {
                    await MainActor.run {
                        self.showError("无法确定帖子作者，删除失败")
                    }
                    return
                }

                guard postAuthor.id == currentUser.id else {
                    await MainActor.run {
                        self.showError("只能删除自己发布的帖子")
                    }
                    return
                }

                // 🔑 第三步：立即关闭页面，避免显示"未知用户"状态
                await MainActor.run {
                    self.shouldDismiss = true
                    self.onPostDeleted?()
                }

                // 🔑 第四步：执行删除操作（在页面关闭后）
                try await repositoryContainer.communityRepository.deletePost(id: self.post.id)

                // 🔑 第五步：发送删除通知，更新社区列表
                await MainActor.run {
                    // 发送帖子删除通知
                    NotificationCenter.default.post(
                        name: NSNotification.Name("EACommunityPostDeleted"),
                        object: self.post.id
                    )

                    #if DEBUG
                    #endif
                }

            } catch let error as NSError {
                await MainActor.run {
                    // 🔑 改进：根据错误类型提供更友好的提示
                    if error.domain == "CommunityError" && error.code == 1 {
                        self.showError("登录状态已过期，请重新登录后再试")
                    } else if error.localizedDescription.contains("权限") {
                        self.showError("权限不足，无法删除此帖子")
                    } else if error.localizedDescription.contains("网络") {
                        self.showError("网络连接异常，请检查网络后重试")
                    } else {
                        self.showError("删除失败，请稍后重试")
                    }

                    #if DEBUG
                    #endif
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /// 是否可以提交评论
    var canSubmitComment: Bool {
        !commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && !isSubmittingComment
    }
    
    /// 是否可以删除帖子（实时权限检查）
    var canDeletePost: Bool {
        // 🔑 修复：实现真实的权限检查
        guard let currentUser = sessionManager.currentUser else {
            #if DEBUG
            #endif
            return false
        }

        // 检查是否为帖子作者
        guard let postAuthor = post.getAuthor() else {
            #if DEBUG
            #endif
            return false
        }

        let canDelete = postAuthor.id == currentUser.id

        #if DEBUG
        #endif

        return canDelete
    }
    
    /// 是否已点赞帖子
    var isPostLiked: Bool {
        // TODO: 实现帖子点赞状态检查
        false // 临时返回false
    }
    
    /// 评论排序文字
    var commentSortText: String {
        switch commentSortOrder {
        case .newest: return "最新"
        case .oldest: return "最早"
        case .mostLiked: return "最热"
        }
    }
    
    /// 检查评论是否已点赞
    func isCommentLiked(_ comment: EACommunityComment) -> Bool {
        // TODO: 实现评论点赞状态检查
        return false // 临时返回false
    }
    
    // MARK: - Private Methods
    
    /// 处理加载错误 - 新增错误恢复机制
    private func handleLoadingError(_ message: String) {
        isLoadingComments = false
        
        // 🔑 修复：根据错误类型决定是否可重试
        if message.contains("数据上下文未初始化") {
            loadingError = "数据环境正在准备中，请稍后..."
            canRetry = true
        } else {
            loadingError = message
            canRetry = true
        }
        
        showError(message)
    }
    
    /// 排序评论
    private func sortComments() {
        switch commentSortOrder {
        case .newest:
            comments.sort { $0.creationDate > $1.creationDate }
        case .oldest:
            comments.sort { $0.creationDate < $1.creationDate }
        case .mostLiked:
            comments.sort { getCommentLikesCount($0) > getCommentLikesCount($1) }
        }
    }
    
    /// 获取帖子点赞数量 - 通过Repository
    func getPostLikesCount() -> Int {
            // 使用Repository异步获取，这里返回缓存值或0
        return 0 // TODO: 实现缓存机制和异步加载
    }
    
    /// 获取评论点赞数量 - 通过Repository
    func getCommentLikesCount(_ comment: EACommunityComment) -> Int {
            // 使用Repository异步获取，这里返回缓存值或0
        return 0 // TODO: 实现缓存机制和异步加载
    }
    
    /// 显示错误消息
    private func showError(_ message: String) {
        alertMessage = message
        showAlert = true
    }
    
    /// 显示提示消息
    private func showAlert(message: String) {
        alertMessage = message
        showAlert = true
    }
    
    /// 获取当前用户（增强版用户认证检查）
    private func getCurrentUser() async throws -> EAUser {
        // 🔑 第一步：检查sessionManager中的用户
        if let currentUser = sessionManager.currentUser {
            // 验证用户数据的有效性
            if await isUserDataValid(currentUser) {
                return currentUser
            }
        }

        // 🔑 第二步：尝试从Repository重新获取用户数据
        if let repositoryUser = try await repositoryContainer.getCurrentUser() {
            // 更新sessionManager的用户状态
            await MainActor.run {
                sessionManager.currentUser = repositoryUser
                sessionManager.isLoggedIn = true
                // 触发UI更新
                sessionManager.objectWillChange.send()
            }

            #if DEBUG
            #endif

            return repositoryUser
        }

        // 🔑 第三步：尝试触发会话恢复
        await MainActor.run {
            sessionManager.restoreSession()
        }

        // 延迟重试一次
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

        if let retryUser = sessionManager.currentUser {
            return retryUser
        }

        // 🔑 最终：所有尝试都失败，抛出用户友好的错误
        throw NSError(
            domain: "CommunityError",
            code: 1,
            userInfo: [
                NSLocalizedDescriptionKey: "登录状态已过期，请重新登录",
                NSLocalizedRecoverySuggestionErrorKey: "请返回登录页面重新登录"
            ]
        )
    }

    /// 🔑 新增：验证用户数据有效性
    private func isUserDataValid(_ user: EAUser) async -> Bool {
        // 检查用户基本信息
        guard !user.username.isEmpty else { return false }

        // 检查用户是否在数据库中存在
        do {
            let existingUser = try await repositoryContainer.userRepository.fetchUser(id: user.id)
            return existingUser != nil
        } catch {
            #if DEBUG
            #endif
            return false
        }
    }
}

// MARK: - CommentSortOrder

/// 评论排序方式
enum CommentSortOrder {
    case newest    // 最新
    case oldest    // 最早
    case mostLiked // 最热
}

// MARK: - Helper Types

/// 评论提交数据结构
private struct CommentSubmissionData {
    let content: String
    let user: EAUser
    let post: EACommunityPost
    let parentComment: EACommunityComment?
}