import Foundation
import SwiftData
import SwiftUI
import Combine

/// 🔑 性能优化：帖子交互数据结构
struct EAPostInteractionData {
    let likeCount: Int
    let commentCount: Int
    let shareCount: Int
    let lastUpdated: Date
    
    init(likeCount: Int = 0, commentCount: Int = 0, shareCount: Int = 0) {
        self.likeCount = likeCount
        self.commentCount = commentCount
        self.shareCount = shareCount
        self.lastUpdated = Date()
    }
}

/// 简化的帖子状态管理结构
struct PostUIState {
    let postId: UUID
    var isLiked: Bool = false
    var likeCount: Int = 0
    var commentCount: Int = 0
    var isVisible: Bool = false
    var interactionData: EAPostInteractionData? = nil

    init(post: EACommunityPost) {
        self.postId = post.id
        self.likeCount = post.likeCount
        self.commentCount = post.commentCount
        self.isVisible = false
        self.interactionData = nil
    }
}

/// 社区ViewModel错误类型
enum EACommunityViewModelError: LocalizedError {
    case repositoryNotAvailable
    case userNotLoggedIn
    case postNotFound
    case operationFailed(String)

    var errorDescription: String? {
        switch self {
        case .repositoryNotAvailable:
            return "数据访问服务不可用"
        case .userNotLoggedIn:
            return "用户未登录"
        case .postNotFound:
            return "帖子不存在"
        case .operationFailed(let message):
            return "操作失败：\(message)"
        }
    }
}

/// 社区页面ViewModel - 管理社区帖子列表和交互逻辑
/// 严格遵循项目MVVM架构规范，使用@MainActor确保UI更新在主线程
@MainActor
final class EACommunityViewModel: ObservableObject {
    
    // MARK: - 发布状态属性
    
    /// 帖子列表
    @Published var posts: [EACommunityPost] = []
    
    /// 加载状态
    @Published var isLoading: Bool = false
    
    /// 错误消息
    @Published var errorMessage: String?
    
    /// 是否还有下一页
    @Published var hasNextPage: Bool = true
    
    /// 是否正在刷新（区别于普通加载）
    @Published var isRefreshing: Bool = false
    
    /// 筛选状态管理
    @Published var selectedCategory: String? = nil
    @Published var selectedTags: [String] = []
    @Published var availableCategories: [String] = []
    @Published var popularTags: [String] = []
    @Published var isFilterActive: Bool = false
    
    /// 页面大小
    private let pageSize: Int = 20
    
    /// 当前页码
    @Published var currentPage: Int = 0
    
    // MARK: - 依赖注入
    
    /// Repository容器（依赖注入，遵循Repository模式）
    private var repositoryContainer: EARepositoryContainer?
    
    /// 🔒 修复：性能监控器依赖注入，移除单例使用
    private var performanceMonitor: EAPerformanceMonitor

    /// 🔗 分享服务
    private var shareService: EACommunityShareService?

    // MARK: - 私有属性（性能优化）

    /// 上次刷新时间，用于防抖
    private var lastRefreshTime: Date = Date.distantPast

    /// 搜索防抖Timer
    private var searchDebounceTimer: Timer?

    /// 简化的数据缓存机制
    private var cachedPosts: [EACommunityPost] = []
    private var cacheTimestamp: Date = Date.distantPast
    private let cacheValidDuration: TimeInterval = 300 // 5分钟缓存

    /// 页面状态管理
    @Published var isViewActive: Bool = false

    /// 🔑 修复：评论数量实时更新监听
    @Published var postCommentCounts: [UUID: Int] = [:]
    
    // MARK: - 性能监控属性（简化版）
    
    /// 网络状态
    @Published var networkStatus: EANetworkStatus = .unknown
    
    /// 性能指标
    @Published var performanceMetrics: EAImageLoadingMetrics = EAImageLoadingMetrics()
    
    /// 内存使用情况
    @Published var memoryUsage: Double = 0.0
    
    // MARK: - 简化的帖子状态管理
    
    /// 帖子UI状态缓存
    @Published private var postUIStates: [UUID: PostUIState] = [:]
    
    /// 获取帖子点赞状态
    func getPostLikeStatus(postId: UUID) -> Bool {
        return postUIStates[postId]?.isLiked ?? false
    }
    
    /// 获取帖子点赞数量
    func getPostLikeCount(postId: UUID) -> Int {
        return postUIStates[postId]?.likeCount ?? 0
    }
    
    /// 🔑 新增：批量预载帖子状态（避免单个异步查询）
    func batchPreloadPostStates() async {
        guard !posts.isEmpty else { return }
        
        // 🔑 关键优化：批量查询所有帖子的点赞状态
        await withTaskGroup(of: Void.self) { group in
            for post in posts {
                group.addTask { [weak self] in
                    guard let self = self else { return }
                    if await self.postUIStates[post.id] == nil {
                        let isLiked = await self.getUserLikeStatus(for: post)
                        await MainActor.run {
                            var state = PostUIState(post: post)
                            state.isLiked = isLiked
                            state.likeCount = post.likeCount
                            self.postUIStates[post.id] = state
                        }
                    }
                }
            }
        }
    }
    
    /// 🔑 性能优化：轻量级的帖子出现处理
    func handlePostAppearLightweight(post: EACommunityPost) {
        // 只记录可见状态，不执行复杂操作
        var state = PostUIState(post: post)
        state.isVisible = true
        postUIStates[post.id] = state
        
        // 无限滚动检测（轻量级）
        if posts.count > 0 && posts.last?.id == post.id {
            // 🔑 性能优化：延迟加载更多内容，避免立即执行
            Task { @MainActor in
                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒延迟
                loadMorePostsIfNeeded()
            }
        }
    }
    
    /// 异步初始化帖子状态（标记为过时，使用批量预载替代）
    @available(*, deprecated, message: "使用batchPreloadPostStates()替代")
    func initializePostLikeState(postId: UUID) async {
        // 🔑 优化：如果已存在状态，跳过初始化
        guard postUIStates[postId] == nil else { return }
        
        // 从数据源获取初始状态
        guard let post = posts.first(where: { $0.id == postId }) else { return }
        
        let isLiked = await getUserLikeStatus(for: post)
        
        await MainActor.run {
            var state = PostUIState(post: post)
            state.isLiked = isLiked
            state.likeCount = post.likeCount
            postUIStates[postId] = state
        }
    }
    
    /// 切换帖子点赞状态
    func togglePostLike(postId: UUID) async {
        guard let post = posts.first(where: { $0.id == postId }) else { return }
        
        // 立即更新UI状态
        await MainActor.run {
            if var state = postUIStates[postId] {
                state.isLiked.toggle()
                state.likeCount += state.isLiked ? 1 : -1
                postUIStates[postId] = state
            }
        }
        
        do {
            // 调用原有的点赞逻辑
            try await toggleLike(for: post)
            
            // 重新同步真实状态
            let realIsLiked = await getUserLikeStatus(for: post)
            await MainActor.run {
                if var state = postUIStates[postId] {
                    state.isLiked = realIsLiked
                    state.likeCount = post.likeCount
                    postUIStates[postId] = state
                }
            }
        } catch {
            // 如果操作失败，回滚UI状态
            await MainActor.run {
                if var state = postUIStates[postId] {
                    state.isLiked.toggle() // 回滚
                    state.likeCount += state.isLiked ? 1 : -1 // 回滚
                    postUIStates[postId] = state
                }
            }
        }
    }
    
    /// 🔑 性能优化：清理帖子UI状态
    func clearPostUIStates() {
        postUIStates.removeAll()
    }


    
    // MARK: - 🔒 修复：依赖注入初始化方法

    init(repositoryContainer: EARepositoryContainer? = nil, performanceMonitor: EAPerformanceMonitor? = nil) {
        self.repositoryContainer = repositoryContainer
        // 🔒 修复：性能监控器延迟初始化，避免主线程问题
        self.performanceMonitor = performanceMonitor ?? EAPerformanceMonitor()

        // 🔗 初始化分享服务
        if let container = repositoryContainer {
            self.shareService = EACommunityShareService(repositoryContainer: container)
        }

        // 🔑 新增：绑定性能监控数据
        self.networkStatus = self.performanceMonitor.networkStatus
        self.performanceMetrics = self.performanceMonitor.imageLoadingMetrics
        self.memoryUsage = self.performanceMonitor.memoryUsage

        // 🔑 修复：初始化评论数量监听
        setupCommentCountListeners()
    }
    
    // MARK: - 公共方法
    
    /// 🔒 修复：添加缺失的Repository容器设置方法
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
        // 🔗 同时初始化分享服务
        self.shareService = EACommunityShareService(repositoryContainer: container)
    }

    /// 🔒 新增：检查Repository容器是否已设置
    var hasRepositoryContainer: Bool {
        return repositoryContainer != nil
    }

    // MARK: - 🔗 分享功能

    /// 分享帖子
    /// - Parameters:
    ///   - post: 要分享的帖子
    ///   - sourceView: 分享按钮的视图（用于iPad定位）
    func sharePost(_ post: EACommunityPost, from sourceView: UIView? = nil) {
        shareService?.sharePost(post, from: sourceView)
    }

    /// 复制帖子链接
    /// - Parameter post: 要复制的帖子
    func copyPostLink(_ post: EACommunityPost) {
        shareService?.copyPostLink(post)
    }

    // MARK: - 🔑 评论数量实时更新

    /// 设置评论数量监听器
    private func setupCommentCountListeners() {
        // 监听评论数量变化的通知
        NotificationCenter.default.addObserver(
            forName: .commentCountDidChange,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self,
                  let userInfo = notification.userInfo,
                  let postId = userInfo["postId"] as? UUID,
                  let newCount = userInfo["commentCount"] as? Int else { return }

            self.updatePostCommentCount(postId: postId, newCount: newCount)
        }
    }

    /// 更新帖子评论数量
    /// - Parameters:
    ///   - postId: 帖子ID
    ///   - newCount: 新的评论数量
    private func updatePostCommentCount(postId: UUID, newCount: Int) {
        // 更新本地缓存
        postCommentCounts[postId] = newCount

        // 更新帖子列表中的数据
        if let index = posts.firstIndex(where: { $0.id == postId }) {
            posts[index].commentCount = newCount
        }

        // 更新UI状态缓存
        if var state = postUIStates[postId] {
            state.commentCount = newCount
            postUIStates[postId] = state
        }
    }

    /// 获取帖子评论数量（优先使用缓存）
    /// - Parameter postId: 帖子ID
    /// - Returns: 评论数量
    func getPostCommentCount(postId: UUID) -> Int {
        return postCommentCounts[postId] ?? 0
    }


    
    /// 🔑 性能优化：简化的Tab切换管理方法
    
    /// 优化的Tab切换到活跃状态处理
    func handleTabSwitchToActiveOptimized() async {
        guard !isViewActive else { return }
        
        isViewActive = true
        
        // 🔑 性能优化：只在必要时刷新数据
        if posts.isEmpty || shouldRefreshData() {
            refreshPosts()
        }
    }
    
    /// 优化的Tab切换到非活跃状态处理
    func handleTabSwitchAwayOptimized() async {
        isViewActive = false
        
        // 🔑 性能优化：清理不必要的状态，释放内存
        clearMemoryPressureStates()
    }
    
    /// 检查是否需要刷新数据
    private func shouldRefreshData() -> Bool {
        let timeSinceLastRefresh = Date().timeIntervalSince(lastRefreshTime)
        return timeSinceLastRefresh > 300 // 5分钟后才需要刷新
    }
    
    /// 检查缓存是否有效
    private func isCacheValid() -> Bool {
        return Date().timeIntervalSince(cacheTimestamp) < cacheValidDuration
    }
    
    /// 清理内存压力状态
    private func clearMemoryPressureStates() {
        // 清理帖子UI状态缓存
        postUIStates.removeAll()
        
        // 清理搜索防抖Timer
        searchDebounceTimer?.invalidate()
        searchDebounceTimer = nil
    }
    
    /// 简化的缓存清理方法
    func clearCache() {
        cachedPosts.removeAll()
        cacheTimestamp = Date.distantPast
        postUIStates.removeAll()
    }
    
    /// 加载帖子列表
    func loadPosts(forceRefresh: Bool = false) {
        guard !isLoading else { return }

        // 优先使用缓存
        if !forceRefresh && !isFilterActive && isCacheValid() {
            posts = cachedPosts
            return
        }

        Task {
            await performLoadPosts()
        }
    }
    
    /// 加载更多帖子（无限滚动）
    func loadMorePosts() {
        guard !isLoading && hasNextPage else { return }
        
        Task {
            await performLoadMorePosts()
        }
    }
    
    /// 应用筛选条件
    func applyFilters(category: String?, tags: [String] = []) {
        selectedCategory = category
        selectedTags = tags
        isFilterActive = (category != nil) || !tags.isEmpty
        
        // 重置分页状态
        currentPage = 0
        hasNextPage = true
        posts.removeAll()
        
        // 重新加载数据
        loadPosts(forceRefresh: true)
    }
    
    /// 清除筛选条件
    func clearFilters() {
        selectedCategory = nil
        selectedTags.removeAll()
        isFilterActive = false
        
        // 重置分页状态
        currentPage = 0
        hasNextPage = true
        posts.removeAll()
        
        // 重新加载数据
        loadPosts(forceRefresh: true)
    }
    
    /// 加载筛选选项
    func loadFilterOptions() {
        Task {
            await performLoadFilterOptions()
        }
    }
    
    /// 刷新帖子列表
    func refreshPosts() {
        // 防抖：避免频繁刷新
        let now = Date()
        guard now.timeIntervalSince(lastRefreshTime) > 1.0 else { return }
        lastRefreshTime = now
        
        // 清理UI状态缓存
        clearPostUIStates()
        
        // 重置分页状态
        currentPage = 0
        hasNextPage = true
        
        // 设置刷新状态
        isRefreshing = true
        
        Task {
            await performLoadPosts()
            await MainActor.run {
                isRefreshing = false
            }
        }
    }

    /// 设置视图活跃状态
    func setViewActive(_ active: Bool) async {
        await MainActor.run {
            isViewActive = active
        }
    }
    
    /// 创建新帖子
    /// - Parameters:
    ///   - title: 帖子标题（暂时不使用，保留接口兼容性）
    ///   - content: 帖子内容
    ///   - images: 图片数据数组
    ///   - habitId: 关联的习惯ID（可选）
    func createPost(title: String, content: String, images: [Data] = [], habitId: UUID? = nil) async {
        guard let container = repositoryContainer else {
            errorMessage = "系统初始化未完成"
            return
        }

        isLoading = true
        defer { isLoading = false }

        do {
            // 🔑 修复：添加完善的错误处理
            guard let currentUser = try await container.getCurrentUser() else {
                errorMessage = "用户未登录，请先登录"
                return
            }

            // 验证内容不为空
            let trimmedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)
            guard !trimmedContent.isEmpty else {
                errorMessage = "帖子内容不能为空"
                return
            }

            // 🔑 新增：处理图片上传
            var imageURLs: [String] = []
            if !images.isEmpty {
                let imageProcessor = EAImageProcessor()
                for imageData in images {
                    let imagePath = try await imageProcessor.processAndSaveImage(imageData)
                    imageURLs.append(imagePath)
                }
            }

            // 创建帖子对象（使用正确的初始化方法）
            let post = EACommunityPost(
                content: trimmedContent,
                habitName: nil, // 暂时不关联习惯名称
                category: "general",
                energyLevel: 5
            )

            // 🔑 新增：设置图片URL
            post.imageURLs = imageURLs

            // 🔑 修复：移除ViewModel层的关系设置，让Repository层统一处理
            // 移除：post.author = currentUser

            // 通过Repository创建帖子
            let createdPost = try await container.communityRepository.createPost(post, authorId: currentUser.id)

            // 添加到本地列表顶部
            posts.insert(createdPost, at: 0)

            // 清空错误消息
            errorMessage = nil
            
        } catch {
            // 🔑 修复：详细的错误处理
            if let repoError = error as? EACommunityRepositoryError {
                switch repoError {
                case .userNotFound:
                    errorMessage = "用户会话已失效，请重新登录"
                case .dataCreateFailed:
                    errorMessage = "创建帖子失败，请检查网络连接"
                case .dataSaveFailed:
                    errorMessage = "保存帖子失败，请稍后重试"
                default:
                    errorMessage = "创建帖子失败：\(repoError.localizedDescription)"
                }
            } else {
                errorMessage = "创建帖子失败：\(error.localizedDescription)"
            }
        }
    }
    
    /// 🔑 新增：获取用户对特定帖子的点赞状态
    /// - Parameter post: 目标帖子
    /// - Returns: 是否已点赞
    func getUserLikeStatus(for post: EACommunityPost) async -> Bool {
        guard let container = repositoryContainer else { return false }

        do {
            guard let currentUser = try await container.getCurrentUser() else {
                return false
            }

            let likes = try await container.communityRepository.fetchLikes(for: post.id)
            return likes.contains { like in
                like.user?.id == currentUser.id && like.isActive
            }
        } catch {
            return false
        }
    }

    /// 切换帖子点赞状态
    /// - Parameter post: 要点赞/取消点赞的帖子
    func toggleLike(for post: EACommunityPost) async throws {
        guard let container = repositoryContainer else {
            throw EACommunityViewModelError.repositoryNotAvailable
        }

        guard let currentUser = try await container.getCurrentUser() else {
            throw EACommunityViewModelError.userNotLoggedIn
        }

        // ✅ 新增：Context一致性验证
        #if DEBUG
        
        #endif

        // 🔑 修复：Repository层已经通过syncLikeCount()更新了likeCount，无需手动更新
        let _ = try await container.communityRepository.toggleLike(
            postId: post.id,
            userId: currentUser.id
        )

        // 🔑 修复：重新获取更新后的帖子数据，确保UI显示正确的数量
        if let index = posts.firstIndex(where: { $0.id == post.id }) {
            if let updatedPost = try await container.communityRepository.fetchPost(by: post.id) {
                posts[index] = updatedPost
            }
        }
    }
    
    /// 搜索帖子
    /// - Parameter searchText: 搜索关键词
    func searchPosts(with searchText: String) {
        // 取消之前的搜索
        searchDebounceTimer?.invalidate()
        
        // 防抖处理：延迟0.5秒执行搜索
        searchDebounceTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { _ in
            Task { @MainActor in
                await self.performSearchPosts(searchText: searchText)
            }
        }
    }
    
    // MARK: - 删除帖子功能
    
    /// 删除帖子
    /// - Parameter post: 要删除的帖子
    func deletePost(_ post: EACommunityPost) async {
        guard let container = repositoryContainer else { return }
        
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 🔑 修复：使用增强版用户获取方法
            guard let currentUser = try await getEnhancedCurrentUser(container: container) else {
                errorMessage = "登录状态已过期，请重新登录"
                return
            }

            // 检查权限（只能删除自己的帖子）
            guard let postAuthor = post.getAuthor() else {
                errorMessage = "无法确定帖子作者"
                return
            }

            guard postAuthor.id == currentUser.id else {
                errorMessage = "只能删除自己发布的帖子"
                return
            }

            try await container.communityRepository.deletePost(id: post.id)

            // 从本地列表中移除
            posts.removeAll { $0.id == post.id }

            #if DEBUG
            #endif

        } catch let error as NSError {
            // 🔑 改进：根据错误类型提供友好提示
            if error.domain == "CommunityError" && error.code == 1 {
                errorMessage = "登录状态已过期，请重新登录"
            } else if error.localizedDescription.contains("权限") {
                errorMessage = "权限不足，无法删除此帖子"
            } else if error.localizedDescription.contains("网络") {
                errorMessage = "网络连接异常，请检查网络后重试"
            } else {
                errorMessage = "删除失败，请稍后重试"
            }

            #if DEBUG
            #endif
        }
    }

    /// 🔑 新增：处理帖子删除通知
    func handlePostDeleted(_ postId: UUID) async {
        await MainActor.run {
            // 从本地列表中移除已删除的帖子
            posts.removeAll { $0.id == postId }

            #if DEBUG
            #endif
        }
    }
    
    /// 检查用户是否有删除权限（增强版）
    /// - Parameter post: 要检查的帖子
    /// - Returns: 是否有删除权限
    func canDeletePost(_ post: EACommunityPost) async -> Bool {
        guard let container = repositoryContainer else { return false }

        do {
            // 🔑 修复：使用增强版用户获取方法
            guard let currentUser = try await getEnhancedCurrentUser(container: container) else {
                return false
            }

            guard let postAuthor = post.getAuthor() else {
                return false
            }

            return postAuthor.id == currentUser.id
        } catch {
            #if DEBUG
            #endif
            return false
        }
    }
    
    // MARK: - 评论相关功能
    
    /// 获取指定帖子的评论数量
    /// - Parameter post: 目标帖子
    /// - Returns: 评论数量
    func getCommentCount(for post: EACommunityPost) -> Int {
        // TODO: 通过Repository获取评论数量
        return 0 // 临时返回0，需要实现Repository方法
    }
    
    /// 添加评论
    /// - Parameters:
    ///   - post: 要评论的帖子
    ///   - content: 评论内容
    func addComment(to post: EACommunityPost, content: String) async {
        guard let container = repositoryContainer else { return }
        guard !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        do {
            // 🔑 修复：使用增强版用户获取方法
            guard let currentUser = try await getEnhancedCurrentUser(container: container) else {
                errorMessage = "登录状态已过期，请重新登录"
                return
            }

            // 创建评论对象（使用正确的初始化方法）
            let comment = EACommunityComment(
                content: content.trimmingCharacters(in: .whitespacesAndNewlines)
            )

            // 设置作者和帖子关系
            comment.author = currentUser
            comment.post = post

            let _ = try await container.communityRepository.createComment(comment, for: post.id, authorId: currentUser.id)

            // 更新本地帖子的评论数
            if let index = posts.firstIndex(where: { $0.id == post.id }) {
                posts[index].commentCount += 1
            }

            #if DEBUG
            #endif

        } catch let error as NSError {
            // 🔑 改进：根据错误类型提供友好提示
            if error.domain == "CommunityError" && error.code == 1 {
                errorMessage = "登录状态已过期，请重新登录"
            } else if error.localizedDescription.contains("网络") {
                errorMessage = "网络连接异常，请检查网络后重试"
            } else if error.localizedDescription.contains("内容") {
                errorMessage = "评论内容不符合要求"
            } else {
                errorMessage = "添加评论失败，请稍后重试"
            }

            #if DEBUG
            #endif
        }
    }
    
    // MARK: - 私有方法
    
    /// 🔑 性能优化：优化的帖子数据加载方法
    private func performLoadPosts() async {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        defer {
            Task { @MainActor in
                isLoading = false
            }
        }
        
        guard let container = repositoryContainer else {
            await MainActor.run {
                errorMessage = "系统初始化未完成"
            }
            return
        }
        
        do {
            let fetchedPosts = try await container.communityRepository.fetchPosts(
                limit: pageSize,
                offset: currentPage * pageSize
            )
            
            await MainActor.run {
                if currentPage == 0 {
                    posts = fetchedPosts
                    cachedPosts = fetchedPosts
                    cacheTimestamp = Date()
                } else {
                    posts.append(contentsOf: fetchedPosts)
                    cachedPosts.append(contentsOf: fetchedPosts)
                }
                
                hasNextPage = fetchedPosts.count == pageSize
                currentPage += 1
                lastRefreshTime = Date()
            }
            
            // 🔑 关键优化：加载完成后批量预载状态
            await batchPreloadPostStates()
            
        } catch {
            await MainActor.run {
                errorMessage = "加载星际信标失败：\(error.localizedDescription)"
            }
        }
    }
    
    /// 🔑 优化：执行加载更多帖子（使用性能优化版本）
    private func performLoadMorePosts() async {
        currentPage += 1
        await performOptimizedLoadPosts()
    }
    
    /// 🔑 新增：执行筛选选项加载
    private func performLoadFilterOptions() async {
        guard let container = repositoryContainer else { return }
        
        do {
            let categories = try await container.communityRepository.fetchAvailableCategories()
            let tags = try await container.communityRepository.fetchPopularTags(limit: 20)
            
            await MainActor.run {
                availableCategories = categories
                popularTags = tags
            }
            
        } catch {
            #if DEBUG
            #endif
        }
    }
    
    /// 执行刷新帖子操作
    private func performRefreshPosts() async {
        guard let container = repositoryContainer else {
            errorMessage = "Repository容器未初始化"
            isRefreshing = false
            return
        }
        
        errorMessage = nil
        
        do {
            // 通过Repository获取最新帖子
            let newPosts = try await container.communityRepository.fetchPosts(
                limit: pageSize,
                offset: 0
            )

            posts = newPosts
            currentPage = 1
            hasNextPage = newPosts.count == pageSize

            // 🔑 更新缓存
            updateCache(with: newPosts)

        } catch {
            errorMessage = "刷新失败：\(error.localizedDescription)"
        }
        
        isRefreshing = false
    }
    
    /// 执行搜索帖子操作
    private func performSearchPosts(searchText: String) async {
        guard let container = repositoryContainer else {
            errorMessage = "Repository容器未初始化"
            return
        }
        
        guard !searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            refreshPosts()
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // 通过Repository搜索帖子
            let searchResults = try await container.communityRepository.searchPosts(
                query: searchText.trimmingCharacters(in: .whitespacesAndNewlines),
                limit: 50
            )
            
            posts = searchResults
            isLoading = false
            
        } catch {
            errorMessage = "搜索失败：\(error.localizedDescription)"
            isLoading = false
        }
    }
    
    // MARK: - 用户认证辅助方法

    /// 🔑 新增：增强版用户获取方法
    private func getEnhancedCurrentUser(container: EARepositoryContainer) async throws -> EAUser? {
        // 第一步：尝试从Repository获取用户
        if let user = try await container.getCurrentUser() {
            return user
        }

        // 第二步：尝试从UserDefaults恢复用户
        if let userIdString = UserDefaults.standard.string(forKey: "currentUserId"),
           let userId = UUID(uuidString: userIdString) {
            if let user = try await container.userRepository.fetchUser(id: userId) {
                #if DEBUG
                #endif
                return user
            }
        }

        // 第三步：尝试获取最近用户
        if let recentUser = try await container.userRepository.fetchCurrentUser() {
            #if DEBUG
            #endif
            return recentUser
        }

        return nil
    }

    // MARK: - 开发辅助方法（移除不存在的方法调用）

    /// 🔑 真实数据优化：重置社区数据（纯真实数据版本）
    func resetCommunityData() async {
        await MainActor.run {
            #if DEBUG
            #endif
            
            // 清空所有本地状态
            posts = []
            cachedPosts = []
            cacheTimestamp = Date.distantPast
            currentPage = 0
            hasNextPage = true
            errorMessage = nil
            clearPostUIStates()
            
            #if DEBUG
            #endif
        }

        // 检查Repository容器状态
        guard let container = repositoryContainer else {
            await MainActor.run {
                errorMessage = "Repository容器未初始化"
                #if DEBUG
                #endif
            }
            return
        }

        do {
            // 🔑 纯真实数据策略：只显示用户真实创建的帖子
            
            #if DEBUG
            #endif
            
            // 直接加载现有的所有真实帖子
            let existingPosts = try await container.communityRepository.fetchPosts(limit: 100, offset: 0)
            
            #if DEBUG
            #endif
            
            // 强制刷新显示真实数据
            loadPosts(forceRefresh: true)
            
            await MainActor.run {
                if posts.isEmpty && existingPosts.isEmpty {
                    #if DEBUG
                    #endif
                } else {
                    #if DEBUG
                    #endif
                }
            }
            
        } catch {
            await MainActor.run {
                errorMessage = "数据加载失败: \(error.localizedDescription)"
                #if DEBUG
                #endif
            }
        }
    }

    // MARK: - 新增：缓存管理方法

    /// 更新缓存
    private func updateCache(with posts: [EACommunityPost]) {
        cachedPosts = posts
        cacheTimestamp = Date()

        #if DEBUG
        #endif
    }

    /// 🔑 新增：页面出现时的初始化逻辑（优化版）
    func onAppear() {
        // 🔑 性能优化：智能缓存策略 - 修复闪烁问题
        if isCacheValid() && !posts.isEmpty {
            #if DEBUG
            #endif
            // 🔑 关键修复：直接设置为非加载状态，避免闪烁
            isLoading = false
            return
        }
        
        // 🔑 关键修复：初始化时不立即设置加载状态，避免空状态闪烁
        if posts.isEmpty {
            // 延迟设置加载状态，给数据恢复一个机会
            Task {
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒延迟
                if posts.isEmpty && !isLoading {
                    await MainActor.run {
                        isLoading = true
                    }
                }
            }
        }
        
        // 🔑 性能优化：优先加载核心数据
        loadPosts(forceRefresh: false)
        
        // 🔑 性能优化：延迟加载筛选选项（非关键路径）
        Task.detached(priority: .background) {
            await MainActor.run {
                self.loadFilterOptions()
            }
        }
    }

    // MARK: - 性能监控辅助方法

    /// 🔑 新增：设置性能监控（简化版）
    private func setupPerformanceMonitoring() {
        // 监听内存压力通知
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            // 🔑 修复：确保在主线程执行，避免线程安全问题
            Task { @MainActor [weak self] in
                self?.handleMemoryPressure()
            }
        }
        
        #if DEBUG
        #endif
    }

    /// 🔑 性能优化：增强内存管理
    private func handleMemoryPressure() {
        #if DEBUG
        print("🔥 [Memory] 处理内存压力，当前内存使用: \(performanceMonitor.memoryUsage)MB")
        #endif

        // 1. 清理帖子缓存（保留最近30条）
        if cachedPosts.count > 30 {
            cachedPosts = Array(cachedPosts.prefix(30))
        }

        // 2. 清理当前显示的帖子（保留最近50条）
        if posts.count > 50 {
            posts = Array(posts.prefix(50))
            hasNextPage = true // 重置分页状态
        }

        // 3. 清理UI状态缓存
        clearPostUIStates()

        // 4. 强制清理图片缓存
        URLCache.shared.removeAllCachedResponses()

        // 5. 重置分页状态，减少内存占用
        currentPage = 0

        #if DEBUG
        print("🔥 [Memory] 内存清理完成，缓存帖子数: \(cachedPosts.count)，显示帖子数: \(posts.count)")
        #endif
    }
    
    /// 🔑 新增：检查网络状态并优化加载策略
    private func checkNetworkAndOptimizeLoading() -> Bool {
        // 🔒 修复：使用注入的性能监控器实例
        
        // 更新本地网络状态
        networkStatus = performanceMonitor.networkStatus
        
        // 如果网络断开，使用缓存数据
        if !networkStatus.isConnected {
            if !cachedPosts.isEmpty {
                posts = cachedPosts
                #if DEBUG
                #endif
                return false
            }
        }
        
        // 如果是蜂窝网络，减少加载数量
        if performanceMonitor.networkStatus == .cellular {
            // 在蜂窝网络下减少页面大小
            return true
        }
        
        return true
    }
    
    /// 🔑 新增：优化版加载帖子（考虑性能）
    private func performOptimizedLoadPosts() async {
        // 检查网络状态
        guard checkNetworkAndOptimizeLoading() else { return }
        
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        defer {
            Task { @MainActor in
                isLoading = false
            }
        }
        
        guard let container = repositoryContainer else {
            await MainActor.run {
                errorMessage = "系统初始化未完成"
            }
            return
        }
        
        do {
            let fetchedPosts: [EACommunityPost]
            
                    // 🔑 根据网络状态调整加载策略
        let optimizedPageSize = performanceMonitor.networkStatus == .cellular ? min(pageSize, 10) : pageSize
            
            // 🔑 根据筛选状态选择不同的加载方法
            if isFilterActive {
                fetchedPosts = try await container.communityRepository.fetchPostsWithFilters(
                    category: selectedCategory,
                    tags: selectedTags.isEmpty ? nil : selectedTags,
                    limit: optimizedPageSize,
                    offset: currentPage * optimizedPageSize
                )
            } else {
                fetchedPosts = try await container.communityRepository.fetchPosts(
                    limit: optimizedPageSize,
                    offset: currentPage * optimizedPageSize
                )
            }
            
            await MainActor.run {
                if currentPage == 0 {
                    posts = fetchedPosts
                    // 🔑 更新缓存（仅在无筛选时）
                    if !isFilterActive {
                        cachedPosts = fetchedPosts
                        cacheTimestamp = Date()
                    }
                } else {
                    posts.append(contentsOf: fetchedPosts)
                }
                
                // 检查是否还有下一页
                hasNextPage = fetchedPosts.count == optimizedPageSize
                
                // 🔑 更新性能指标  
                // 🔒 修复：使用注入的性能监控器实例
                performanceMetrics = performanceMonitor.imageLoadingMetrics
                memoryUsage = performanceMonitor.memoryUsage
                
                #if DEBUG
                #endif
            }
            
        } catch {
            await MainActor.run {
                errorMessage = "加载帖子失败：\(error.localizedDescription)"
                #if DEBUG
                #endif
            }
        }
    }
    
    /// 🔑 新增：获取性能优化建议
    func getPerformanceRecommendations() -> [EAPerformanceRecommendation] {
        // 🔒 修复：使用注入的性能监控器实例
        return performanceMonitor.getCommunityPerformanceRecommendations()
    }
    
    // MARK: - 🔒 内存管理和清理方法
    
    /// 清理资源，防止内存泄漏
    func cleanup() {
        // 清理数据
        posts.removeAll()
        cachedPosts.removeAll()
        postUIStates.removeAll()
        
        // 停止定时器和监听
        NotificationCenter.default.removeObserver(self)
        
        // 重置状态
        isLoading = false
        isRefreshing = false
        isViewActive = false
        
        #if DEBUG
        print("🔒 [Community] ViewModel已清理资源")
        #endif
    }
    
    deinit {
        // 🔒 修复：deinit中不能访问@MainActor属性，只清理非主线程相关资源
        
        // 停止定时器和监听
        NotificationCenter.default.removeObserver(self)
        
        #if DEBUG
        print("🔒 [Community] ViewModel已销毁")
        #endif
    }

    /// 🔑 新增：重置分页状态的方法
    func resetPagination() {
        currentPage = 0
        hasNextPage = true
        posts.removeAll()
        clearPostUIStates()
    }

    /// 🔑 性能优化：批量异步数据加载
    private func loadPostInteractionDataBatch() async {
        // 获取当前可见的帖子IDs
        let visiblePostIds = postUIStates.compactMap { key, value in
            value.isVisible ? key : nil
        }
        
        // 批量加载交互数据（点赞、评论数）
        await withTaskGroup(of: (UUID, EAPostInteractionData?).self) { group in
            for postId in visiblePostIds {
                group.addTask { [weak self] in
                    guard let self = self else { return (postId, nil) }
                    let interactionData = await self.loadPostInteractionData(postId: postId)
                    return (postId, interactionData)
                }
            }
            
            // 批量更新UI状态
            for await (postId, interactionData) in group {
                await MainActor.run {
                    self.postUIStates[postId]?.interactionData = interactionData
                }
            }
        }
    }
    
    /// 🔑 性能优化：加载单个帖子交互数据
    private func loadPostInteractionData(postId: UUID) async -> EAPostInteractionData? {
        // 简化实现：基于现有数据生成交互数据
        guard let post = posts.first(where: { $0.id == postId }) else {
            return nil
        }
        
        return EAPostInteractionData(
            likeCount: post.likeCount,
            commentCount: post.commentCount,
            shareCount: 0 // 暂时设为0，等待后续实现
        )
    }
    
    /// 🔑 性能优化：检查是否需要加载更多内容
    private func loadMorePostsIfNeeded() {
        guard !isLoading && hasNextPage else { return }
        loadMorePosts()
    }
}

// MARK: - 错误类型定义

enum CommunityError: LocalizedError {
    case userNotLoggedIn
    case invalidContent
    case networkError
    case dataCorruption
    case dataNotFound
    case insufficientPermissions

    var errorDescription: String? {
        switch self {
        case .userNotLoggedIn:
            return "用户未登录"
        case .invalidContent:
            return "内容格式无效"
        case .networkError:
            return "网络连接错误"
        case .dataCorruption:
            return "数据损坏"
        case .dataNotFound:
            return "数据未找到"
        case .insufficientPermissions:
            return "权限不足"
        }
    }
}

// MARK: - 通知扩展

extension Notification.Name {
    /// 评论数量变化通知
    static let commentCountDidChange = Notification.Name("commentCountDidChange")

    /// 帖子点赞状态变化通知
    static let postLikeStatusDidChange = Notification.Name("postLikeStatusDidChange")

    /// 帖子分享成功通知
    static let postShareDidSuccess = Notification.Name("postShareDidSuccess")
}
