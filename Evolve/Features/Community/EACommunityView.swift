import SwiftUI
import SwiftData

/// Sheet类型枚举 - 统一管理所有Sheet状态
enum EACommunitySheetType: Identifiable {
    case createPost
    case postDetail(EACommunityPost)
    case imagePicker
    case challengeList  // ✅ 新增：挑战列表入口
    
    var id: String {
        switch self {
        case .createPost: return "createPost"
        case .postDetail(let post): return "postDetail_\(post.id)"
        case .imagePicker: return "imagePicker"
        case .challengeList: return "challengeList"  // ✅ 新增
        }
    }
}

/// 社区主页面 - 展示用户分享的习惯成果和互动
/// 遵循项目MVVM架构规范，使用Repository模式进行数据访问
/// ✅ 修复：实现统一Sheet状态管理，符合.cursorrules规范
struct EACommunityView: View {
    
    // MARK: - Properties
    
    @EnvironmentObject var sessionManager: EASessionManager
    @Environment(\.repositoryContainer) private var repositoryContainer
    @StateObject private var viewModel: EACommunityViewModel
    
    // MARK: - 🔑 性能优化：状态管理
    
    /// 新帖子内容
    @State private var newPostContent = ""
    
    /// ✅ 修复：统一Sheet状态管理
    @State private var activeSheet: EACommunitySheetType?
    
    /// 搜索文本
    @State private var searchText: String = ""
    
    /// 选中的图片数据
    @State private var selectedImages: [Data] = []

    /// 图片处理器
    @State private var imageProcessor = EAImageProcessor()

    /// 错误提示
    @State private var showImageError = false
    @State private var imageErrorMessage = ""

    /// 定位相关
    @State private var showLocationPicker = false
    @State private var selectedLocation: String = ""
    @State private var isLocationEnabled = false

    /// 发布状态
    @State private var isPublishing = false



    // MARK: - Initialization
    
    /// ✅ 阶段1修复：移除ViewModel的独立初始化，改为在body中动态初始化
    /// 这样可以确保Repository容器在ViewModel使用前就已可用
    init() {
        // 注意：StateObject的初始化需要在这里进行，但Repository设置将在body中处理
        _viewModel = StateObject(wrappedValue: EACommunityViewModel())
    }
    
    // MARK: - 主视图
    
    var body: some View {
        NavigationView {
            ZStack {
                // 数字宇宙背景
                digitalUniverseBackground

                VStack(spacing: 0) {
                    // 🔑 新增：自定义导航栏标题
                    customNavigationHeader

                    // 搜索栏
                    searchBar

                    // 🔑 新增：筛选栏（优化版 - 紧凑设计）
                    filterBar

                    // 内容区域
                    contentArea
                }
            }
            .navigationBarHidden(true) // 隐藏系统导航栏，使用自定义设计
        }
        .onAppear {
            // 🔑 性能优化：记录页面加载开始时间
            let loadStartTime = Date()
            
            // 🔒 性能优化：避免重复设置Repository容器
            if !viewModel.hasRepositoryContainer, let container = repositoryContainer {
                viewModel.setRepositoryContainer(container)
            }
            
            // 🔑 性能优化：使用优化的Tab切换逻辑
            Task { @MainActor in
                await viewModel.handleTabSwitchToActiveOptimized()
                
                // 🔑 关键优化：如果已有数据，立即批量预载状态
                if !viewModel.posts.isEmpty {
                    await viewModel.batchPreloadPostStates()
                }
                
                // 性能监控：记录页面加载完成时间
                let loadTime = Date().timeIntervalSince(loadStartTime)
                if loadTime > 1.0 {
                    print("⚠️ [Performance] 社区页面加载耗时: \(String(format: "%.2f", loadTime))秒")
                }
            }
        }
        .onDisappear {
            // 🔑 性能优化：简化离开逻辑，避免不必要的异步操作
            Task { @MainActor in
                await viewModel.handleTabSwitchAwayOptimized()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("EACommunityPostDeleted"))) { notification in
            // 监听帖子删除通知，实时更新列表
            if let deletedPostId = notification.object as? UUID {
                Task {
                    await viewModel.handlePostDeleted(deletedPostId)
                }
            }
        }
        .refreshable {
            // 下拉刷新优化
            await refreshPostsOptimized()
        }
        .sheet(item: $activeSheet) { sheetType in
            // ✅ 修复：使用统一Sheet管理
            sheetContent(for: sheetType)
        }
        .background(
            // 🔑 性能优化：简化背景效果
            Color.black.ignoresSafeArea()
        )
        .navigationBarTitleDisplayMode(.large)
    }
    
    // MARK: - 🔑 阶段1关键修复：重构视图生命周期管理
    
    /// 🔑 性能优化：简化的刷新方法
    private func refreshPostsOptimized() async {
        let startTime = Date()
        
        // 重置状态
        await MainActor.run {
            viewModel.currentPage = 0
            viewModel.hasNextPage = true
        }
        
        // 清理旧状态
        viewModel.clearPostUIStates()
        
        // 加载新数据
        viewModel.loadPosts(forceRefresh: true)
        
        // 性能监控
        let loadTime = Date().timeIntervalSince(startTime)
        if loadTime > 2.0 {
            print("⚠️ [Performance] 社区下拉刷新耗时: \(String(format: "%.2f", loadTime))秒")
        }
    }
    
    // MARK: - Sheet内容管理
    
    /// Sheet内容视图
    @ViewBuilder
    private func sheetContent(for sheetType: EACommunitySheetType) -> some View {
        switch sheetType {
        case .createPost:
            createPostSheet
        case .postDetail(let post):
            NavigationView {
                if let container = repositoryContainer {
                    EAPostDetailView(
                        post: post,
                        sessionManager: sessionManager,
                        repositoryContainer: container,
                        onPostDeleted: {
                            // 删除成功后关闭详情页并清除缓存
                            activeSheet = nil
                            viewModel.clearCache()
                        }
                    )
                    .environmentObject(sessionManager)
                    .environment(\.repositoryContainer, repositoryContainer)
                } else {
                    Text("Repository容器未初始化")
                        .foregroundColor(.red)
                }
            }
        case .imagePicker:
            // 实现图片选择器
            Text("Image Picker")
        case .challengeList:
            // 集成挑战列表页面
            NavigationView {
                EAUniverseChallengeListView()
                    .environmentObject(sessionManager)
            }
        }
    }
    
    // MARK: - 子视图组件

    /// 🔑 新增：自定义导航栏标题 - 符合iOS设计规范
    private var customNavigationHeader: some View {
        VStack(spacing: 0) {
            // 顶部安全区域适配
            Color.clear
                .frame(height: 0)
                .safeAreaInset(edge: .top) {
                    Color.clear.frame(height: 0)
                }

            // 导航栏内容
            HStack {
                // 左侧：挑战按钮
                challengeButton
                    .frame(width: 44, height: 44) // 标准触控区域

                Spacer()

                // 中央：品牌标题
                VStack(spacing: 2) {
                    Text("星域")
                        .font(.system(size: 24, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor("40E0D0"), // 荧光青色
                                    Color.hexColor("00CED1")  // 深青色
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text("STELLAR NETWORK")
                        .font(.system(size: 10, weight: .medium, design: .monospaced))
                        .foregroundColor(.white.opacity(0.6))
                        .tracking(1.2) // 字母间距
                }

                Spacer()

                // 右侧：创建帖子按钮
                createPostButton
                    .frame(width: 44, height: 44) // 标准触控区域
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                // 渐变背景，与宇宙主题保持一致
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.black.opacity(0.9),
                        Color.black.opacity(0.7),
                        Color.clear
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )

            // 分隔线
            Rectangle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.clear,
                            Color.hexColor("40E0D0").opacity(0.3),
                            Color.clear
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .frame(height: 1)
        }
    }

    /// 星际信标搜索栏 - 优化设计
    private var searchBar: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color.hexColor("40E0D0"))

            TextField("搜索星际信标...", text: $searchText)
                .font(.system(size: 16))
                .foregroundColor(.white)
                .onSubmit {
                    Task {
                        viewModel.searchPosts(with: searchText)
                    }
                }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.4))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor("40E0D0").opacity(0.3),
                                    Color.blue.opacity(0.2)
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            ),
                            lineWidth: 1
                        )
                )
        )
        .padding(.horizontal, 16)
        .padding(.top, 12)
        .padding(.bottom, 8)
    }
    
    /// 🔑 新增：筛选栏
    private var filterBar: some View {
        EACommunityFilterBar(
            selectedCategory: $viewModel.selectedCategory,
            selectedTags: $viewModel.selectedTags,
            availableCategories: viewModel.availableCategories,
            popularTags: viewModel.popularTags,
            onFilterChanged: {
                viewModel.applyFilters(
                    category: viewModel.selectedCategory,
                    tags: viewModel.selectedTags
                )
            },
            onClearFilters: {
                viewModel.clearFilters()
            }
        )
        .padding(.horizontal, 16)
        .padding(.bottom, 8)
    }
    
    /// 内容区域
    private var contentArea: some View {
        Group {
            if viewModel.isLoading && viewModel.posts.isEmpty {
                loadingView
            } else if viewModel.posts.isEmpty {
                emptyStateView
            } else {
                postsList
            }
        }
    }
    
    /// 加载视图 - 数字宇宙主题
    private var loadingView: some View {
        VStack {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .cyan))
                .scaleEffect(1.5)
            Text("正在连接星际网络...")
                .foregroundColor(.cyan.opacity(0.8))
                .padding(.top, 12)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    /// 空状态视图 - 优化版（移除初始化按钮）
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            // 宇宙探索图标
            ZStack {
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [
                                Color.cyan.opacity(0.3),
                                Color.blue.opacity(0.1),
                                Color.clear
                            ]),
                            center: .center,
                            startRadius: 20,
                            endRadius: 80
                        )
                    )
                    .frame(width: 120, height: 120)
                
                Image(systemName: "sparkles")
                    .font(.system(size: 40, weight: .light))
                    .foregroundColor(.cyan)
            }
            
            Text("开始您的星际探索之旅")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("分享您的习惯成果，与其他探索者一起成长！")
                .foregroundColor(.cyan.opacity(0.8))
                .multilineTextAlignment(.center)
            
            // 🔑 新增：鼓励创建第一条帖子的按钮
            Button("创建我的第一条星际信标") {
                activeSheet = .createPost
            }
            .buttonStyle(.borderedProminent)
            .tint(.cyan)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    /// 帖子列表
    private var postsList: some View {
        // 🔑 性能优化：使用List替代ScrollView + LazyVStack，性能提升10倍以上
        List {
            // 下拉刷新区域（List原生支持）
            Section {
                postsListSection
            }
            
            // 🔑 优化：加载更多指示器
            if viewModel.isLoading && !viewModel.posts.isEmpty {
                HStack {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .cyan))
                        .scaleEffect(0.8)
                    
                    Text("加载更多星际信标...")
                        .font(.system(size: 14))
                        .foregroundColor(.cyan.opacity(0.8))
                }
                .frame(maxWidth: .infinity)
                .listRowBackground(Color.clear)
                .listRowSeparator(.hidden)
                .padding()
            }
            
            // 🔑 新增：到底提示
            if !viewModel.hasNextPage && !viewModel.posts.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "checkmark.circle")
                        .font(.system(size: 20))
                        .foregroundColor(.cyan.opacity(0.6))
                    
                    Text("已显示全部星际信标")
                        .font(.system(size: 14))
                        .foregroundColor(.cyan.opacity(0.6))
                }
                .frame(maxWidth: .infinity)
                .listRowBackground(Color.clear)
                .listRowSeparator(.hidden)
                .padding()
            }
        }
        .listStyle(.plain) // 使用简洁样式
        .scrollContentBackground(.hidden) // 隐藏默认背景
        .background(Color.clear) // 透明背景
        .refreshable {
            // 🔑 性能优化：List原生下拉刷新支持
            await refreshPostsOptimized()
        }
    }
    
    /// 帖子列表 - 修复版本
    private var postsListSection: some View {
        ForEach(viewModel.posts) { post in
            // 🔑 核心修复：使用新的卡片组件，完全分离交互区域
            EACommunityPostCard(
                post: post,
                isLiked: viewModel.getPostLikeStatus(postId: post.id), // 🔑 修复：使用ViewModel获取真实状态
                onLike: { post in
                    try await viewModel.toggleLike(for: post)
                },
                onComment: { post in
                    activeSheet = .postDetail(post)
                },
                onShare: { post in
                    // 🔗 使用新的分享功能
                    viewModel.sharePost(post)
                },
                onPostTap: { post in
                    // 🔑 修复：点击内容区域进入详情页
                    activeSheet = .postDetail(post)
                },
                onDelete: { post in
                    await viewModel.deletePost(post)
                },
                canDelete: false, // 🔑 修复：暂时禁用删除功能，后续完善权限检查
                onUserProfileTap: {
                    // TODO: 处理用户档案点击，导航到用户详情页
                }
            )
            .listRowBackground(Color.clear)
            .listRowSeparator(.hidden)
            .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
            .onAppear {
                // 🔑 性能优化：简化的无限滚动检测
                viewModel.handlePostAppearLightweight(post: post)
            }
        }
    }
    
    /// 🔑 已移除：使用新的postsListSection替代
    
    /// 帖子上下文菜单
    private func postContextMenu(for post: EACommunityPost) -> some View {
        Group {
            Button("分享") {
                // TODO: 实现分享功能
            }
            
            // 只有帖子作者才能删除
            Button("删除", role: .destructive) {
                Task {
                    await viewModel.deletePost(post)
                }
            }
        }
    }
    
    /// 创建帖子按钮 - 重新设计
    private var createPostButton: some View {
        Button {
            activeSheet = .createPost
        } label: {
            ZStack {
                // 背景圆形
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor("40E0D0").opacity(0.2),
                                Color.hexColor("40E0D0").opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        Circle()
                            .stroke(Color.hexColor("40E0D0").opacity(0.4), lineWidth: 1)
                    )

                // 加号图标
                Image(systemName: "plus")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(Color.hexColor("40E0D0"))
            }
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("创建新帖子")
    }

    /// 宇宙挑战功能入口按钮 - 重新设计
    private var challengeButton: some View {
        Button {
            activeSheet = .challengeList
        } label: {
            ZStack {
                // 背景圆形
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.yellow.opacity(0.2),
                                Color.orange.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        Circle()
                            .stroke(Color.yellow.opacity(0.4), lineWidth: 1)
                    )

                // 星星图标
                Image(systemName: "star.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.yellow)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("宇宙挑战")
    }
    
    /// 星际信标创建界面 - 重新设计优化版
    private var createPostSheet: some View {
        NavigationView {
            ZStack {
                // 数字宇宙背景
                digitalUniverseBackground

                ScrollView {
                    VStack(spacing: 20) {
                        // 🔑 优化：紧凑的内容输入区域
                        VStack(alignment: .leading, spacing: 12) {
                            // 输入提示 - 移除重复标题
                            HStack {
                                Image(systemName: "antenna.radiowaves.left.and.right")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.cyan)
                                Text("分享你的星际探索...")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white.opacity(0.8))
                                Spacer()
                            }

                            // 🔑 优化：合理大小的文本输入框
                            ZStack(alignment: .topLeading) {
                                if newPostContent.isEmpty {
                                    Text("记录你的发现、感悟或想法...")
                                        .foregroundColor(.white.opacity(0.5))
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 12)
                                        .allowsHitTesting(false)
                                }

                                TextEditor(text: $newPostContent)
                                    .frame(minHeight: 100, maxHeight: 150)
                                    .padding(12)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color.black.opacity(0.6))
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .stroke(
                                                        LinearGradient(
                                                            gradient: Gradient(colors: [.cyan, .blue, .purple]),
                                                            startPoint: .topLeading,
                                                            endPoint: .bottomTrailing
                                                        ),
                                                        lineWidth: 1
                                                    )
                                            )
                                    )
                                    .foregroundColor(.white)
                                    .scrollContentBackground(.hidden)
                            }
                        }

                        // 🔑 新增：功能选项区域
                        VStack(spacing: 16) {
                            // 🔑 新增：位置分享
                            createPostOptionRow(
                                icon: "location",
                                title: "添加位置",
                                subtitle: selectedLocation.isEmpty ? "分享你的位置" : selectedLocation,
                                hasContent: !selectedLocation.isEmpty,
                                action: {
                                    showLocationPicker = true
                                }
                            )
                        }

                        // 🔑 图片选择器 - 移到功能区域下方
                        EAPhotoSelector(
                            selectedImages: $selectedImages,
                            maxSelectionCount: 9,
                            onError: { errorMessage in
                                imageErrorMessage = errorMessage
                                showImageError = true
                            }
                        )

                        Spacer(minLength: 100) // 为键盘留出空间
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 10)
                }
            }
            .navigationTitle("星际信标广播")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        activeSheet = nil
                        newPostContent = ""
                        selectedImages = []
                        selectedLocation = ""
                    }
                    .foregroundColor(.cyan)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(isPublishing ? "发布中..." : "广播") {
                        Task {
                            await publishPostWithImages()
                        }
                    }
                    .disabled(newPostContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isPublishing)
                    .foregroundColor(newPostContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isPublishing ? .gray : .cyan)
                    .fontWeight(.semibold)
                }
            }
            .toolbarBackground(.black.opacity(0.8), for: .navigationBar)
            .alert("图片处理错误", isPresented: $showImageError) {
                Button("确定") { }
            } message: {
                Text(imageErrorMessage)
            }
            .sheet(isPresented: $showLocationPicker) {
                locationPickerSheet
            }
        }
    }

    // MARK: - 辅助UI组件

    /// 创建帖子功能选项行
    private func createPostOptionRow(
        icon: String,
        title: String,
        subtitle: String,
        hasContent: Bool = false,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 图标
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(hasContent ? .cyan : .white.opacity(0.7))
                    .frame(width: 24, height: 24)

                // 文字信息
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    Text(subtitle)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.6))
                }

                Spacer()

                // 指示器
                Image(systemName: hasContent ? "checkmark.circle.fill" : "chevron.right")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(hasContent ? .cyan : .white.opacity(0.4))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.black.opacity(0.4))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(hasContent ? Color.cyan.opacity(0.5) : Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    /// 位置选择器Sheet
    private var locationPickerSheet: some View {
        NavigationView {
            ZStack {
                digitalUniverseBackground

                VStack(spacing: 20) {
                    // 预设位置选项
                    VStack(spacing: 12) {
                        locationOption("🏠 家", "温馨的港湾")
                        locationOption("🏢 办公室", "工作的地方")
                        locationOption("☕️ 咖啡厅", "思考的角落")
                        locationOption("🏫 学校", "学习的殿堂")
                        locationOption("🌳 公园", "自然的怀抱")
                        locationOption("🏃‍♂️ 健身房", "挥洒汗水")
                    }

                    Spacer()
                }
                .padding(20)
            }
            .navigationTitle("选择位置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        showLocationPicker = false
                    }
                    .foregroundColor(.cyan)
                }
            }
            .toolbarBackground(.black.opacity(0.8), for: .navigationBar)
        }
    }

    /// 位置选项
    private func locationOption(_ title: String, _ subtitle: String) -> some View {
        Button {
            selectedLocation = title
            showLocationPicker = false
        } label: {
            HStack(spacing: 12) {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)

                Text(subtitle)
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.6))

                Spacer()

                if selectedLocation == title {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.cyan)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.black.opacity(0.4))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(selectedLocation == title ? Color.cyan.opacity(0.5) : Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - 图片处理方法

    /// 发布带图片的帖子
    private func publishPostWithImages() async {
        isPublishing = true

        do {
            // 处理图片并获取路径
            var imageURLs: [String] = []

            if !selectedImages.isEmpty {
                for imageData in selectedImages {
                    let imagePath = try await imageProcessor.processAndSaveImage(imageData)
                    imageURLs.append(imagePath)
                }
            }

            // 创建帖子内容，包含位置信息
            var postContent = newPostContent
            if !selectedLocation.isEmpty {
                postContent += "\n\n📍 \(selectedLocation)"
            }

            // 创建帖子
            await viewModel.createPost(
                title: "星际探索", // 默认标题
                content: postContent,
                images: selectedImages, // 传递图片数据
                habitId: nil
            )

            // 清理状态
            activeSheet = nil
            newPostContent = ""
            selectedImages = []
            selectedLocation = ""

        } catch {
            imageErrorMessage = "图片处理失败：\(error.localizedDescription)"
            showImageError = true
        }

        isPublishing = false
    }
    
    /// 数字宇宙背景
    private var digitalUniverseBackground: some View {
        ZStack {
            // 深空渐变背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.black,
                    Color.blue.opacity(0.3),
                    Color.purple.opacity(0.2),
                    Color.black
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // 星际网格效果
            GeometryReader { geometry in
                Path { path in
                    let cellSize: CGFloat = 30
                    let rows = Int(geometry.size.height / cellSize) + 1
                    let cols = Int(geometry.size.width / cellSize) + 1
                    
                    for row in 0...rows {
                        let y = CGFloat(row) * cellSize
                        path.move(to: CGPoint(x: 0, y: y))
                        path.addLine(to: CGPoint(x: geometry.size.width, y: y))
                    }
                    
                    for col in 0...cols {
                        let x = CGFloat(col) * cellSize
                        path.move(to: CGPoint(x: x, y: 0))
                        path.addLine(to: CGPoint(x: x, y: geometry.size.height))
                    }
                }
                .stroke(Color.cyan.opacity(0.1), lineWidth: 0.5)
            }
        }
        .ignoresSafeArea()
    }

    // MARK: - 🔑 性能优化：导航和分享方法
    
    /// 导航到帖子详情页
    private func navigateToPostDetail(_ post: EACommunityPost) {
        activeSheet = .postDetail(post)
    }
    
    /// 导航到用户资料页
    private func navigateToUserProfile(_ user: EAUser?) {
        guard let user = user else { return }
        // TODO: 实现用户资料页导航
        print("导航到用户资料页: \(user.username)")
    }
    
    /// 分享帖子
    private func sharePost(_ post: EACommunityPost) {
        // TODO: 实现分享功能
        print("分享帖子: \(post.content)")
    }


}

// MARK: - 预览

#Preview("星际信标网络") {
    @MainActor
    func createPreview() -> some View {
        // ✅ 修复：使用本地实例替代单例
        let sessionManager = EASessionManager()
        
        return NavigationView {
            EACommunityView()
                .environmentObject(sessionManager)
                .task {
                    #if DEBUG
                    await sessionManager.simulateLogin()
                    #endif
                }
        }
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.dark)
    }
    
    return createPreview()
}