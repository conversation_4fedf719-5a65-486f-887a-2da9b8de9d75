import Foundation
import SwiftData

/// 社区帖子模型 - 用户分享习惯成果和能量状态
/// 严格遵循SwiftData单端inverse规则，只在必要的一端使用@Relationship
@Model
final class EACommunityPost: @unchecked Sendable {
    var id: UUID = UUID()
    var content: String                  // 帖子内容
    var imageURLs: [String] = []        // 图片URL数组
    var creationDate: Date = Date()      // 创建时间
    var lastEditDate: Date = Date()      // 最后编辑时间
    var isVisible: Bool = true           // 是否可见（用于软删除）
    var likeCount: Int = 0              // 点赞数量
    var commentCount: Int = 0           // 评论数量
    var shareCount: Int = 0             // 分享数量
    
    // 分类和标签
    var category: String = "general"     // 帖子分类：general(一般)、achievement(成就)、challenge(挑战)
    var tags: [String] = []             // 标签数组
    
    // 习惯相关信息
    var habitName: String?              // 相关习惯名称
    var habitCategory: String?          // 习惯分类
    var completionStreakDays: Int = 0   // 连续完成天数
    
    // 能量系统
    var energyLevel: Int = 5            // 发帖时的能量等级 (1-10)
    var energyBoost: Int = 0            // 帖子获得的能量提升
    
    // 数字宇宙星际能量系统（兼容性扩展，所有属性为可选类型）
    var stellarEnergyValue: Int? = nil    // 帖子的星际能量价值 - 可选类型确保兼容性
    var cosmicResonance: Double? = nil   // 宇宙共振系数 (影响能量传播) - 可选类型确保兼容性
    var universalImpact: Int? = nil        // 宇宙影响力点数 (基于互动计算) - 可选类型确保兼容性
    var stellarBoostMultiplier: Double? = nil  // 星际能量加成倍数 - 可选类型确保兼容性
    var isCosmicMilestone: Bool? = nil // 是否为宇宙里程碑帖子 - 可选类型确保兼容性
    var stellarCategory: String? = nil  // 星际分类：探索分享、成就展示、挑战宣言、能量传递 - 可选类型确保兼容性
    
    // 内容审核
    var moderationStatus: String = "approved"  // 审核状态：pending、approved、rejected
    var moderationDate: Date?                  // 审核时间
    var flagCount: Int = 0                     // 被举报次数
    
    // AI增强功能
    var aiGeneratedTags: [String] = []  // AI生成的标签
    var sentimentScore: Double = 0.0    // 情感分析分数 (-1.0 到 1.0)
    var isAISuggested: Bool = false     // 是否为AI建议发布
    
    // 🔗 SwiftData关系：与用户社交档案的关系（普通属性，EAUserSocialProfile端定义inverse）
    var authorSocialProfile: EAUserSocialProfile?
    
    // 🔗 SwiftData关系：与评论的一对多关系（核心关系，EACommunityPost端管理）
    @Relationship(deleteRule: .cascade, inverse: \EACommunityComment.post)
    var comments: [EACommunityComment] = []
    
    // 🔗 SwiftData关系：与点赞的一对多关系（核心关系，EACommunityPost端管理）
    @Relationship(deleteRule: .cascade, inverse: \EACommunityLike.targetPost)
    var likes: [EACommunityLike] = []
    
    init(
        content: String,
        habitName: String? = nil,
        category: String = "general",
        energyLevel: Int = 5
    ) {
        self.content = content
        self.habitName = habitName
        self.category = category
        self.energyLevel = energyLevel
        
        // ✅ 关键修复：数组在init中初始化，遵循iOS 18.2要求
        self.imageURLs = []
        self.tags = []
        self.aiGeneratedTags = []
        self.comments = []
        self.likes = []
        
        // 数字宇宙属性保持为nil，在需要时通过initializeDigitalUniverseData()初始化
    }
    
    // MARK: - Convenience Methods
    
    /// 获取作者用户名（安全访问）
    func getAuthorUsername() -> String {
        return authorSocialProfile?.user?.username ?? "星际探索者"
    }
    
    /// 获取作者邮箱（安全访问）
    func getAuthorEmail() -> String {
        return authorSocialProfile?.user?.email ?? ""
    }
    
    /// 获取作者用户（通过社交档案）
    func getAuthor() -> EAUser? {
        return authorSocialProfile?.user
    }
    
    /// 检查是否为作者发布
    func isAuthoredBy(_ user: EAUser) -> Bool {
        return authorSocialProfile?.user?.id == user.id
    }
    
    /// 获取有效评论数量（通过关系访问）
    func getVisibleCommentsCount() -> Int {
        return comments.filter { $0.isVisible }.count
    }

    /// ✅ 新增：获取实际的有效点赞数量（通过关系访问）
    func getActiveLikesCount() -> Int {
        return likes.filter { $0.isActive }.count
    }

    /// ✅ 新增：同步likeCount字段与实际的likes关系数组
    func syncLikeCount() {
        likeCount = getActiveLikesCount()
    }

    /// ✅ 新增：同步commentCount字段与实际的comments关系数组
    func syncCommentCount() {
        commentCount = getVisibleCommentsCount()
    }
    
    // MARK: - 数字宇宙星际能量系统方法
    
    /// 初始化数字宇宙数据（兼容性方法）
    func initializeDigitalUniverseData(stellarCategory: String = "探索分享") {
        guard stellarEnergyValue == nil else { return } // 避免重复初始化
        
        self.stellarCategory = stellarCategory
        self.stellarEnergyValue = calculateBaseStellarEnergy()
        self.cosmicResonance = 1.0
        self.universalImpact = 0
        self.stellarBoostMultiplier = 1.0
        self.isCosmicMilestone = false
    }
    
    /// 计算基础星际能量值
    private func calculateBaseStellarEnergy() -> Int {
        var baseEnergy = 10
        
        // 根据帖子类型调整基础能量
        switch stellarCategory {
        case "成就展示":
            baseEnergy = 20
        case "挑战宣言":
            baseEnergy = 15
        case "能量传递":
            baseEnergy = 25
        default: // 探索分享
            baseEnergy = 10
        }
        
        // 根据内容长度微调
        if content.count > 100 {
            baseEnergy += 5
        }
        
        return baseEnergy
    }
    
    /// 更新宇宙影响力 (基于互动数据)
    func updateUniversalImpact() {
        initializeDigitalUniverseData()
        
        let likeWeight = 1
        let commentWeight = 3
        let shareWeight = 5
        
        universalImpact = (likeCount * likeWeight) + 
                         (commentCount * commentWeight) + 
                         (shareCount * shareWeight)
        
        // 更新宇宙共振系数
        updateCosmicResonance()
    }
    
    /// 更新宇宙共振系数
    private func updateCosmicResonance() {
        initializeDigitalUniverseData()
        
        let baseResonance = 1.0
        let currentImpact = universalImpact ?? 0
        let impactBonus = Double(currentImpact) / 100.0
        let energyBonus = Double(energyLevel) / 20.0
        
        let newResonance = baseResonance + impactBonus + energyBonus
        
        // 限制最大共振系数
        cosmicResonance = min(newResonance, 5.0)
    }
    
    /// 计算总星际能量输出 (包含所有加成)
    func calculateTotalStellarEnergyOutput() -> Int {
        initializeDigitalUniverseData()
        
        let baseEnergy = Double(stellarEnergyValue ?? 10)
        let currentResonance = cosmicResonance ?? 1.0
        let currentMultiplier = stellarBoostMultiplier ?? 1.0
        let isMilestone = isCosmicMilestone ?? false
        
        let resonanceBoost = baseEnergy * (currentResonance - 1.0)
        let multiplierBoost = baseEnergy * (currentMultiplier - 1.0)
        let milestoneBonus = isMilestone ? baseEnergy * 0.5 : 0
        
        return Int(baseEnergy + resonanceBoost + multiplierBoost + milestoneBonus)
    }
    
    /// 标记为宇宙里程碑帖子
    func markAsCosmicMilestone() {
        initializeDigitalUniverseData()
        
        isCosmicMilestone = true
        let currentMultiplier = stellarBoostMultiplier ?? 1.0
        stellarBoostMultiplier = max(currentMultiplier, 2.0)
    }
    
    /// 获取星际能量等级描述
    func getStellarEnergyLevelDescription() -> String {
        let totalEnergy = calculateTotalStellarEnergyOutput()
        
        switch totalEnergy {
        case 0...20:
            return "微光星尘"
        case 21...50:
            return "星光闪烁"
        case 51...100:
            return "恒星光辉"
        case 101...200:
            return "星系能量"
        default:
            return "宇宙之力"
        }
    }
    
    /// 检查是否能触发能量共振
    func canTriggerEnergyResonance() -> Bool {
        let currentResonance = cosmicResonance ?? 1.0
        let currentImpact = universalImpact ?? 0
        return currentResonance >= 2.0 && currentImpact >= 50
    }
} 