//
//  EACommunityRepository.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-05.
//

import Foundation
import SwiftData
import SwiftUI

// MARK: - Repository Error Types
enum EACommunityRepositoryError: LocalizedError {
    case contextMismatch
    case userNotFound
    case postNotFound
    case commentNotFound
    case likeNotFound
    case dataFetchFailed
    case dataCreateFailed
    case dataSaveFailed
    case invalidParameters
    
    var errorDescription: String? {
        switch self {
        case .contextMismatch:
            return "数据上下文不匹配"
        case .userNotFound:
            return "用户未找到"
        case .postNotFound:
            return "帖子未找到"
        case .commentNotFound:
            return "评论未找到"
        case .likeNotFound:
            return "点赞未找到"
        case .dataFetchFailed:
            return "数据获取失败"
        case .dataCreateFailed:
            return "数据创建失败"
        case .dataSaveFailed:
            return "数据保存失败"
        case .invalidParameters:
            return "参数无效"
        }
    }
}

// MARK: - Community Repository Protocol
protocol EACommunityRepositoryProtocol {
    // 帖子管理
    func fetchPosts(limit: Int, offset: Int) async throws -> [EACommunityPost]
    func fetchPost(by id: UUID) async throws -> EACommunityPost?
    func fetchUserPosts(userId: UUID, limit: Int, includeHidden: Bool) async throws -> [EACommunityPost]
    func fetchUserFollowing(userId: UUID, limit: Int) async throws -> [EACommunityFollow]
    func fetchUserFollowers(userId: UUID, limit: Int) async throws -> [EACommunityFollow]
    func isUserFollowing(followerId: UUID, followeeId: UUID) async throws -> Bool
    func fetchMutualFollows(userId: UUID) async throws -> [EACommunityFollow]
    func createPost(_ post: EACommunityPost, authorId: UUID) async throws -> EACommunityPost
    func updatePost(_ post: EACommunityPost) async throws -> EACommunityPost
    func deletePost(id: UUID) async throws
    
    // 评论管理
    func fetchComments(for postId: UUID) async throws -> [EACommunityComment]
    func createComment(_ comment: EACommunityComment, for postId: UUID, authorId: UUID) async throws -> EACommunityComment
    func createComment(content: String, authorId: UUID, postId: UUID, parentCommentId: UUID?) async throws -> EACommunityComment
    func deleteComment(id: UUID) async throws
    
    // 点赞管理
    func toggleLike(postId: UUID, userId: UUID) async throws -> Bool
    func fetchLikes(for postId: UUID) async throws -> [EACommunityLike]
    func findExistingLike(commentId: UUID, userId: UUID) async throws -> EACommunityLike?
    func deleteLike(_ likeId: UUID) async throws
    func createLike(targetType: String, targetCommentId: UUID?, userId: UUID, userEnergyLevel: Int) async throws -> EACommunityLike
    
    // 用户关系
    func followUser(followerId: UUID, followingId: UUID) async throws
    func unfollowUser(followerId: UUID, followingId: UUID) async throws
    func isFollowing(followerId: UUID, followingId: UUID) async throws -> Bool

    // ✅ 新增：数据同步方法
    func syncAllPostCounts() async throws
    
    // 搜索功能
    func searchPosts(query: String, limit: Int) async throws -> [EACommunityPost]
    func searchUsers(query: String, limit: Int) async throws -> [EAUser]
    
    // 🔑 新增：分类和标签筛选功能
    func fetchPostsByCategory(category: String, limit: Int, offset: Int) async throws -> [EACommunityPost]
    func fetchPostsByTags(tags: [String], limit: Int, offset: Int) async throws -> [EACommunityPost]
    func fetchPostsWithFilters(category: String?, tags: [String]?, limit: Int, offset: Int) async throws -> [EACommunityPost]
    func fetchAvailableCategories() async throws -> [String]
    func fetchPopularTags(limit: Int) async throws -> [String]
}

// MARK: - Community Repository Implementation
@ModelActor
actor EACommunityRepository: EACommunityRepositoryProtocol {
    
    // MARK: - Helper Methods
    
    /// 根据用户ID获取用户
    private func getUser(by userId: UUID) throws -> EAUser {
        let descriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate<EAUser> { user in
                user.id == userId
            }
        )
        
        guard let user = try modelContext.fetch(descriptor).first else {
            throw EACommunityRepositoryError.userNotFound
        }
        
        return user
    }
    
    /// 安全保存Context
    private func saveContext() throws {
        do {
            try modelContext.save()
        } catch {
            throw EACommunityRepositoryError.dataSaveFailed
        }
    }
    
    // MARK: - Post Management
    
    func fetchPosts(limit: Int, offset: Int) async throws -> [EACommunityPost] {
        let descriptor = FetchDescriptor<EACommunityPost>(
            predicate: #Predicate<EACommunityPost> { $0.isVisible == true },
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        
        do {
            let allPosts = try modelContext.fetch(descriptor)
            
            // 手动实现分页优化性能
            let startIndex = min(offset, allPosts.count)
            let endIndex = min(offset + limit, allPosts.count)
            
            guard startIndex < allPosts.count else { return [] }
            
            return Array(allPosts[startIndex..<endIndex])
            
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchPost(by id: UUID) async throws -> EACommunityPost? {
        let descriptor = FetchDescriptor<EACommunityPost>(
            predicate: #Predicate<EACommunityPost> { post in
                post.id == id
            }
        )
        
        do {
            return try modelContext.fetch(descriptor).first
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchUserPosts(userId: UUID, limit: Int = 10, includeHidden: Bool = false) async throws -> [EACommunityPost] {
        do {
            // 🔑 修复：简化查询，避免复杂的可选链比较
            let descriptor = FetchDescriptor<EACommunityPost>(
                predicate: #Predicate<EACommunityPost> { post in
                    post.isVisible || includeHidden
                },
                sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
            )
            
            let allPosts = try modelContext.fetch(descriptor)
            
            // 在内存中过滤用户的帖子
            let userPosts = allPosts.filter { post in
                post.authorSocialProfile?.user?.id == userId
            }
            
            return Array(userPosts.prefix(limit))
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchUserFollowing(userId: UUID, limit: Int = 10) async throws -> [EACommunityFollow] {
        do {
            // 🔑 修复：简化查询，在内存中过滤
            let descriptor = FetchDescriptor<EACommunityFollow>(
                predicate: #Predicate<EACommunityFollow> { follow in
                    follow.isActive
                },
                sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
            )
            
            let allFollows = try modelContext.fetch(descriptor)
            
            // 在内存中过滤用户的关注关系
            let userFollowing = allFollows.filter { follow in
                follow.followerProfile?.user?.id == userId
            }
            
            return Array(userFollowing.prefix(limit))
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchUserFollowers(userId: UUID, limit: Int = 10) async throws -> [EACommunityFollow] {
        do {
            // 🔑 修复：简化查询，在内存中过滤
            let descriptor = FetchDescriptor<EACommunityFollow>(
                predicate: #Predicate<EACommunityFollow> { follow in
                    follow.isActive
                },
                sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
            )
            
            let allFollows = try modelContext.fetch(descriptor)
            
            // 在内存中过滤用户的粉丝关系
            let userFollowers = allFollows.filter { follow in
                follow.followeeProfile?.user?.id == userId
            }
            
            return Array(userFollowers.prefix(limit))
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func isUserFollowing(followerId: UUID, followeeId: UUID) async throws -> Bool {
        do {
            // 🔑 修复：简化查询，在内存中过滤
            let descriptor = FetchDescriptor<EACommunityFollow>(
                predicate: #Predicate<EACommunityFollow> { follow in
                    follow.isActive
                }
            )
            
            let allFollows = try modelContext.fetch(descriptor)
            
            // 在内存中查找匹配的关注关系
            let matchingFollow = allFollows.first { follow in
                follow.followerProfile?.user?.id == followerId &&
                follow.followeeProfile?.user?.id == followeeId
            }
            
            return matchingFollow != nil
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchMutualFollows(userId: UUID) async throws -> [EACommunityFollow] {
        do {
            // 🔑 修复：简化查询，在内存中处理
            let descriptor = FetchDescriptor<EACommunityFollow>(
                predicate: #Predicate<EACommunityFollow> { follow in
                    follow.isActive
                }
            )
            
            let allFollows = try modelContext.fetch(descriptor)
            
            // 在内存中找出用户的关注和粉丝
            let userFollowing = allFollows.filter { follow in
                follow.followerProfile?.user?.id == userId
            }
            
            let userFollowers = allFollows.filter { follow in
                follow.followeeProfile?.user?.id == userId
            }
            
            // 找出互相关注的用户
            let followingUserIds = Set(userFollowing.compactMap { $0.followeeProfile?.user?.id })
            let mutualFollows = userFollowers.filter { follow in
                guard let followerUserId = follow.followerProfile?.user?.id else { return false }
                return followingUserIds.contains(followerUserId)
            }
            
            return mutualFollows
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func createPost(_ post: EACommunityPost, authorId: UUID) async throws -> EACommunityPost {
        do {
            // 获取当前用户
            let currentUser = try getUser(by: authorId)
            
            // 插入帖子到Context
            modelContext.insert(post)
            
            // 🔑 修复：使用新的关系模式，通过社交档案建立关系
            // 由于EACommunityPost.authorSocialProfile有正确的inverse配置
            post.authorSocialProfile = currentUser.socialProfile
            
            // 保存Context
            try saveContext()
            
            return post
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataCreateFailed
            }
        }
    }
    
    func updatePost(_ post: EACommunityPost) async throws -> EACommunityPost {
        do {
            // 验证帖子存在
            guard let _ = try await fetchPost(by: post.id) else {
                throw EACommunityRepositoryError.postNotFound
            }
            
            // 更新时间戳
            post.lastEditDate = Date()
            
            // 保存Context
            try saveContext()
            
            return post
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataSaveFailed
            }
        }
    }
    
    func deletePost(id: UUID) async throws {
        guard let post = try await fetchPost(by: id) else {
            throw EACommunityRepositoryError.postNotFound
        }
        
        do {
            modelContext.delete(post)
            try saveContext()
        } catch {
            throw EACommunityRepositoryError.dataSaveFailed
        }
    }
    
    // MARK: - Comment Management
    
    func fetchComments(for postId: UUID) async throws -> [EACommunityComment] {
        let descriptor = FetchDescriptor<EACommunityComment>(
            predicate: #Predicate<EACommunityComment> { comment in
                comment.post?.id == postId
            },
            sortBy: [SortDescriptor(\.creationDate, order: .forward)]
        )
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func createComment(_ comment: EACommunityComment, for postId: UUID, authorId: UUID) async throws -> EACommunityComment {
        do {
            // 获取当前用户和目标帖子
            let currentUser = try getUser(by: authorId)
            guard let targetPost = try await fetchPost(by: postId) else {
                throw EACommunityRepositoryError.postNotFound
            }
            
            // 插入评论到Context
            modelContext.insert(comment)
            
            // 🔑 修复：只设置必要的关系，避免重复设置
            comment.author = currentUser
            comment.post = targetPost
            
            // 保存Context
            try saveContext()
            
            return comment
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataCreateFailed
            }
        }
    }
    
    func createComment(content: String, authorId: UUID, postId: UUID, parentCommentId: UUID?) async throws -> EACommunityComment {
        do {
            // 获取作者和目标帖子
            let author = try getUser(by: authorId)
            
            guard let targetPost = try await fetchPost(by: postId) else {
                throw EACommunityRepositoryError.postNotFound
            }
            
            // 创建评论
            let newComment = EACommunityComment(content: content)
            modelContext.insert(newComment)
            
            // 建立关系
            newComment.author = author
            newComment.post = targetPost
            
            // 如果有父评论，建立父子关系
            if let parentId = parentCommentId {
                let parentDescriptor = FetchDescriptor<EACommunityComment>(
                    predicate: #Predicate<EACommunityComment> { comment in comment.id == parentId }
                )
                if let parentComment = try modelContext.fetch(parentDescriptor).first {
                    newComment.parentComment = parentComment
                }
            }
            
            try saveContext()
            return newComment
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataCreateFailed
            }
        }
    }
    
    func deleteComment(id: UUID) async throws {
        let descriptor = FetchDescriptor<EACommunityComment>(
            predicate: #Predicate<EACommunityComment> { comment in
                comment.id == id
            }
        )
        
        do {
            guard let comment = try modelContext.fetch(descriptor).first else {
                throw EACommunityRepositoryError.commentNotFound
            }
            
            modelContext.delete(comment)
            try saveContext()
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataSaveFailed
            }
        }
    }
    
    // MARK: - Like Management
    
    func toggleLike(postId: UUID, userId: UUID) async throws -> Bool {
        do {
            // ✅ 新增：Context一致性验证
            #if DEBUG
            print("🔍 toggleLike - 使用Context ID: \(ObjectIdentifier(modelContext))")
            #endif

            // 🔑 修复：简化查询，避免复杂的对象比较
            let likesDescriptor = FetchDescriptor<EACommunityLike>(
                predicate: #Predicate<EACommunityLike> { like in
                    like.targetType == "post"
                }
            )

            let allLikes = try modelContext.fetch(likesDescriptor)

            // 在内存中查找现有的点赞
            let existingLike = allLikes.first { like in
                like.user?.id == userId && like.targetPost?.id == postId && like.isActive
            }

            if let like = existingLike {
                // 已存在，执行取消点赞
                like.isActive = false  // 🔑 优化：软删除，保留数据用于分析

                // ✅ 修复：同步更新帖子的likeCount字段
                if let targetPost = try await fetchPost(by: postId) {
                    targetPost.syncLikeCount()
                }

                try saveContext()
                return false
            } else {
                // 检查是否有被软删除的点赞记录
                let inactiveLike = allLikes.first { like in
                    like.user?.id == userId && like.targetPost?.id == postId && !like.isActive
                }

                if let inactiveLike = inactiveLike {
                    // 重新激活现有记录
                    inactiveLike.relike()
                } else {
                    // 创建新点赞记录
                    let currentUser = try getUser(by: userId)
                    guard let targetPost = try await fetchPost(by: postId) else {
                        throw EACommunityRepositoryError.postNotFound
                    }

                    let newLike = EACommunityLike(
                        targetType: "post"
                    )

                    // 插入到Context
                    modelContext.insert(newLike)

                    // 建立关系
                    newLike.user = currentUser
                    newLike.targetPost = targetPost
                }

                // ✅ 修复：同步更新帖子的likeCount字段
                if let targetPost = try await fetchPost(by: postId) {
                    targetPost.syncLikeCount()
                }

                try saveContext()
                return true
            }
        } catch {
            #if DEBUG
            print("❌ toggleLike失败: \(error)")
            #endif

            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataSaveFailed
            }
        }
    }
    
    func fetchLikes(for postId: UUID) async throws -> [EACommunityLike] {
        // 🔑 修复：简化查询，在内存中过滤
        let descriptor = FetchDescriptor<EACommunityLike>(
            predicate: #Predicate<EACommunityLike> { like in
                like.targetType == "post"
            },
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        
        do {
            let allLikes = try modelContext.fetch(descriptor)
            
            // 在内存中过滤特定帖子的点赞
            let postLikes = allLikes.filter { like in
                like.targetPost?.id == postId
            }
            
            return postLikes
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func findExistingLike(commentId: UUID, userId: UUID) async throws -> EACommunityLike? {
        // 🔑 修复：简化查询，在内存中过滤
        let descriptor = FetchDescriptor<EACommunityLike>(
            predicate: #Predicate<EACommunityLike> { like in
                like.targetType == "comment"
            }
        )
        
        do {
            let allLikes = try modelContext.fetch(descriptor)
            
            // 在内存中查找匹配的点赞
            let matchingLike = allLikes.first { like in
                like.targetComment?.id == commentId && like.user?.id == userId
            }
            
            return matchingLike
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func deleteLike(_ likeId: UUID) async throws {
        let descriptor = FetchDescriptor<EACommunityLike>(
            predicate: #Predicate<EACommunityLike> { like in like.id == likeId }
        )
        
        do {
            guard let like = try modelContext.fetch(descriptor).first else {
                throw EACommunityRepositoryError.likeNotFound
            }
            
            modelContext.delete(like)
            try saveContext()
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataSaveFailed
            }
        }
    }
    
    func createLike(targetType: String, targetCommentId: UUID?, userId: UUID, userEnergyLevel: Int) async throws -> EACommunityLike {
        do {
            // 获取用户
            let user = try getUser(by: userId)
            
            // 创建点赞
            let newLike = EACommunityLike(targetType: targetType, userEnergyLevel: userEnergyLevel)
            modelContext.insert(newLike)
            
            // 建立关系
            newLike.user = user
            
            // 如果是评论点赞，建立与评论的关系
            if let commentId = targetCommentId {
                let commentDescriptor = FetchDescriptor<EACommunityComment>(
                    predicate: #Predicate<EACommunityComment> { comment in comment.id == commentId }
                )
                if let comment = try modelContext.fetch(commentDescriptor).first {
                    newLike.targetComment = comment
                }
            }
            
            try saveContext()
            return newLike
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataCreateFailed
            }
        }
    }
    
    // MARK: - User Relationship Management
    
    func followUser(followerId: UUID, followingId: UUID) async throws {
        do {
            // 防止自己关注自己
            guard followerId != followingId else {
                throw EACommunityRepositoryError.invalidParameters
            }
            
            // 🔑 修复：简化查询，在内存中处理
            let usersDescriptor = FetchDescriptor<EAUser>(
                predicate: #Predicate<EAUser> { user in
                    user.id == followerId || user.id == followingId
                }
            )
            
            let users = try modelContext.fetch(usersDescriptor)
            guard let followerUser = users.first(where: { $0.id == followerId }),
                  let followingUser = users.first(where: { $0.id == followingId }) else {
                throw EACommunityRepositoryError.userNotFound
            }
            
            // 确保用户有社交档案，如果没有则创建
            if followerUser.socialProfile == nil {
                let socialProfile = EAUserSocialProfile()
                socialProfile.user = followerUser
                modelContext.insert(socialProfile)
                followerUser.socialProfile = socialProfile
            }
            
            if followingUser.socialProfile == nil {
                let socialProfile = EAUserSocialProfile()
                socialProfile.user = followingUser
                modelContext.insert(socialProfile)
                followingUser.socialProfile = socialProfile
            }
            
            // 检查是否已经关注（在内存中检查）
            let followsDescriptor = FetchDescriptor<EACommunityFollow>()
            let allFollows = try modelContext.fetch(followsDescriptor)
            
            let existingFollow = allFollows.first { follow in
                follow.followerProfile?.user?.id == followerId &&
                follow.followeeProfile?.user?.id == followingId
            }
            
            guard existingFollow == nil else {
                // 已经关注了，无需重复操作
                return
            }
            
            // 创建关注关系
            let newFollow = EACommunityFollow()
            
            // 插入到Context
            modelContext.insert(newFollow)
            
            // 建立关系（通过社交档案）
            newFollow.followerProfile = followerUser.socialProfile
            newFollow.followeeProfile = followingUser.socialProfile
            
            try saveContext()
        } catch {
            if error is EACommunityRepositoryError {
                throw error
            } else {
                throw EACommunityRepositoryError.dataSaveFailed
            }
        }
    }
    
    func unfollowUser(followerId: UUID, followingId: UUID) async throws {
        do {
            // 🔑 修复：简化查询，在内存中处理
            let followsDescriptor = FetchDescriptor<EACommunityFollow>()
            let allFollows = try modelContext.fetch(followsDescriptor)
            
            // 在内存中查找要取消的关注关系
            let followToRemove = allFollows.first { follow in
                follow.followerProfile?.user?.id == followerId &&
                follow.followeeProfile?.user?.id == followingId
            }
            
            guard let follow = followToRemove else {
                // 没有关注关系，无需取消
                return
            }
            
            modelContext.delete(follow)
            try saveContext()
        } catch {
            throw EACommunityRepositoryError.dataSaveFailed
        }
    }
    
    func isFollowing(followerId: UUID, followingId: UUID) async throws -> Bool {
        do {
            // 🔑 修复：简化查询，在内存中处理
            let followsDescriptor = FetchDescriptor<EACommunityFollow>()
            let allFollows = try modelContext.fetch(followsDescriptor)
            
            // 在内存中查找匹配的关注关系
            let matchingFollow = allFollows.first { follow in
                follow.followerProfile?.user?.id == followerId &&
                follow.followeeProfile?.user?.id == followingId
            }
            
            return matchingFollow != nil
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    // MARK: - Search Functionality
    
    func searchPosts(query: String, limit: Int = 20) async throws -> [EACommunityPost] {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else { return [] }
        
        // 🔑 修复：简化Predicate，避免编译器超时
        let descriptor = FetchDescriptor<EACommunityPost>(
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        
        do {
            let allPosts = try modelContext.fetch(descriptor)
            // 在内存中进行搜索，避免复杂的Predicate
            let filteredPosts = allPosts.filter { post in
                post.content.localizedCaseInsensitiveContains(trimmedQuery)
            }
            return Array(filteredPosts.prefix(limit))
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func searchUsers(query: String, limit: Int = 20) async throws -> [EAUser] {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else { return [] }
        
        // 🔑 修复：简化Predicate，避免编译器超时
        let descriptor = FetchDescriptor<EAUser>(
            sortBy: [SortDescriptor(\.username, order: .forward)]
        )
        
        do {
            let allUsers = try modelContext.fetch(descriptor)
            // 在内存中进行搜索，避免复杂的Predicate
            let filteredUsers = allUsers.filter { user in
                user.username.localizedCaseInsensitiveContains(trimmedQuery)
            }
            return Array(filteredUsers.prefix(limit))
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }

    // MARK: - 数据同步方法

    /// ✅ 新增：同步所有帖子的计数字段与实际关系数组
    func syncAllPostCounts() async throws {
        do {
            let descriptor = FetchDescriptor<EACommunityPost>()
            let allPosts = try modelContext.fetch(descriptor)

            for post in allPosts {
                post.syncLikeCount()
                post.syncCommentCount()
            }

            try saveContext()
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }

    // 🔑 新增：分类和标签筛选功能（完全简化版）
    func fetchPostsByCategory(category: String, limit: Int, offset: Int) async throws -> [EACommunityPost] {
        // 🔑 修复：完全避免复杂Predicate，使用简单查询+内存筛选
        let descriptor = FetchDescriptor<EACommunityPost>(
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        
        do {
            let allPosts = try modelContext.fetch(descriptor)
            let filteredPosts = allPosts.filter { $0.category == category }
            
            let startIndex = min(offset, filteredPosts.count)
            let endIndex = min(offset + limit, filteredPosts.count)
            
            guard startIndex < endIndex else { return [] }
            
            return Array(filteredPosts[startIndex..<endIndex])
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchPostsByTags(tags: [String], limit: Int, offset: Int) async throws -> [EACommunityPost] {
        // 🔑 修复：完全简化，避免复杂的数组操作
        let descriptor = FetchDescriptor<EACommunityPost>(
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        
        do {
            let allPosts = try modelContext.fetch(descriptor)
            
            // 简化的标签匹配逻辑
            let filteredPosts = allPosts.filter { post in
                for tag in tags {
                    if post.tags.contains(tag) {
                        return true
                    }
                }
                return false
            }
            
            let startIndex = min(offset, filteredPosts.count)
            let endIndex = min(offset + limit, filteredPosts.count)
            
            guard startIndex < endIndex else { return [] }
            
            return Array(filteredPosts[startIndex..<endIndex])
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchPostsWithFilters(category: String?, tags: [String]?, limit: Int, offset: Int) async throws -> [EACommunityPost] {
        // 🔑 修复：完全简化筛选逻辑
        let descriptor = FetchDescriptor<EACommunityPost>(
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        
        do {
            let allPosts = try modelContext.fetch(descriptor)
            
            // 简化的筛选逻辑
            let filteredPosts = allPosts.filter { post in
                // 分类筛选
                if let category = category, post.category != category {
                    return false
                }
                
                // 标签筛选
                if let tags = tags, !tags.isEmpty {
                    var hasMatchingTag = false
                    for tag in tags {
                        if post.tags.contains(tag) {
                            hasMatchingTag = true
                            break
                        }
                    }
                    if !hasMatchingTag {
                        return false
                    }
                }
                
                return true
            }
            
            let startIndex = min(offset, filteredPosts.count)
            let endIndex = min(offset + limit, filteredPosts.count)
            
            guard startIndex < endIndex else { return [] }
            
            return Array(filteredPosts[startIndex..<endIndex])
        } catch {
            throw EACommunityRepositoryError.dataFetchFailed
        }
    }
    
    func fetchAvailableCategories() async throws -> [String] {
        let descriptor = FetchDescriptor<EACommunityPost>()
        let allPosts = try modelContext.fetch(descriptor)
        let categories = Set(allPosts.map { $0.category })
        return Array(categories).sorted()
    }
    
    func fetchPopularTags(limit: Int) async throws -> [String] {
        let descriptor = FetchDescriptor<EACommunityPost>()
        let allPosts = try modelContext.fetch(descriptor)
        
        // 简化的标签统计逻辑
        var tagCounts: [String: Int] = [:]
        for post in allPosts {
            for tag in post.tags {
                tagCounts[tag, default: 0] += 1
            }
        }
        
        let sortedTags = tagCounts.sorted { $0.value > $1.value }
        return Array(sortedTags.prefix(limit).map { $0.key })
    }

}