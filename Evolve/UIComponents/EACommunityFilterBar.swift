import SwiftUI

/// 社区筛选栏组件 - 提供分类和标签筛选功能
/// 遵循EA命名规范和数字宇宙主题设计
/// 🔑 优化：紧凑设计，可折叠，减少屏幕空间占用
struct EACommunityFilterBar: View {
    
    // MARK: - 绑定属性
    
    @Binding var selectedCategory: String?
    @Binding var selectedTags: [String]
    
    let availableCategories: [String]
    let popularTags: [String]
    let onFilterChanged: () -> Void
    let onClearFilters: () -> Void
    
    // MARK: - 状态属性
    
    @State private var showTagSelector: Bool = false
    @State private var isExpanded: Bool = false // 🔑 新增：折叠状态
    
    // MARK: - 分类映射
    
    private let categoryDisplayNames: [String: String] = [
        "general": "探索分享",
        "achievement": "成就展示", 
        "challenge": "挑战宣言"
    ]
    
    private let categoryIcons: [String: String] = [
        "general": "antenna.radiowaves.left.and.right",
        "achievement": "star.circle.fill",
        "challenge": "flame.fill"
    ]
    
    var body: some View {
        VStack(spacing: 0) {
            // 🔑 新增：紧凑的筛选头部（始终显示）
            compactFilterHeader
            
            // 🔑 新增：可展开的详细筛选区域
            if isExpanded {
                expandedFilterContent
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .background(filterBarBackground)
        .animation(.easeInOut(duration: 0.3), value: isExpanded)
    }
    
    // MARK: - 紧凑筛选头部
    
    private var compactFilterHeader: some View {
        HStack(spacing: 12) {
            // 筛选图标和状态
            HStack(spacing: 6) {
                Image(systemName: "line.3.horizontal.decrease.circle")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                
                Text(filterStatusText)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.9))
            }
            
            Spacer()
            
            // 快速分类选择（最多显示3个）
            if !availableCategories.isEmpty {
                quickCategoryButtons
            }
            
            // 展开/收起按钮
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            }) {
                Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
    
    // MARK: - 快速分类按钮
    
    private var quickCategoryButtons: some View {
        HStack(spacing: 6) {
            // 全部按钮
            quickCategoryButton(
                title: "全部",
                icon: "circle.grid.3x3",
                isSelected: selectedCategory == nil,
                action: {
                    selectedCategory = nil
                    onFilterChanged()
                }
            )
            
            // 前3个分类
            ForEach(Array(availableCategories.prefix(3)), id: \.self) { category in
                quickCategoryButton(
                    title: categoryDisplayNames[category] ?? category,
                    icon: categoryIcons[category] ?? "circle",
                    isSelected: selectedCategory == category,
                    action: {
                        selectedCategory = category
                        onFilterChanged()
                    }
                )
            }
        }
    }
    
    // MARK: - 展开的筛选内容
    
    private var expandedFilterContent: some View {
        VStack(spacing: 16) {
            Divider()
                .background(Color.white.opacity(0.2))
            
            // 完整分类筛选
            if availableCategories.count > 3 {
                fullCategorySection
            }
            
            // 标签筛选区域
            if !popularTags.isEmpty {
                tagFilterSection
            }
            
            // 筛选操作按钮
            filterActionButtons
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
    }
    
    // MARK: - 完整分类区域
    
    private var fullCategorySection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("所有分类")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                
                Spacer()
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                ForEach(availableCategories, id: \.self) { category in
                    categoryButton(
                        title: categoryDisplayNames[category] ?? category,
                        icon: categoryIcons[category] ?? "circle",
                        isSelected: selectedCategory == category,
                        action: {
                            selectedCategory = category
                            onFilterChanged()
                        }
                    )
                }
            }
        }
    }
    
    // MARK: - 标签筛选区域（简化版）
    
    private var tagFilterSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("热门标签")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                
                Spacer()
                
                if selectedTags.count > 0 {
                    Text("已选 \(selectedTags.count)")
                        .font(.system(size: 12))
                        .foregroundColor(Color.hexColor("40E0D0"))
                }
            }
            
            // 🔑 优化：使用网格布局，更紧凑
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 6) {
                ForEach(popularTags.prefix(8), id: \.self) { tag in
                    compactTagButton(
                        title: tag,
                        isSelected: selectedTags.contains(tag),
                        action: {
                            toggleTag(tag)
                        }
                    )
                }
            }
        }
    }
    
    // MARK: - 筛选操作按钮
    
    private var filterActionButtons: some View {
        HStack(spacing: 12) {
            // 清除筛选按钮
            if selectedCategory != nil || !selectedTags.isEmpty {
                Button("清除筛选") {
                    onClearFilters()
                }
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.red.opacity(0.8))
            }
            
            Spacer()
            
            // 收起按钮
            Button("收起") {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded = false
                }
            }
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(Color.hexColor("40E0D0"))
        }
    }
    
    // MARK: - 辅助视图
    
    private var filterBarBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color.black.opacity(0.4))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor("40E0D0").opacity(0.3),
                                Color.blue.opacity(0.2)
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        ),
                        lineWidth: 1
                    )
            )
    }
    
    private var filterStatusText: String {
        if selectedCategory != nil || !selectedTags.isEmpty {
            return "筛选已应用"
        } else {
            return "筛选"
        }
    }
    
    // MARK: - 按钮组件
    
    private func quickCategoryButton(title: String, icon: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 10, weight: .medium))
                
                Text(title)
                    .font(.system(size: 11, weight: .medium))
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(isSelected ? Color.hexColor("40E0D0").opacity(0.3) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 6)
                            .stroke(
                                isSelected ? Color.hexColor("40E0D0") : Color.white.opacity(0.3),
                                lineWidth: 1
                            )
                    )
            )
            .foregroundColor(isSelected ? Color.hexColor("40E0D0") : .white.opacity(0.8))
        }
    }
    
    private func categoryButton(title: String, icon: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                
                Text(title)
                    .font(.system(size: 11, weight: .medium))
                    .multilineTextAlignment(.center)
            }
            .frame(height: 50)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.hexColor("40E0D0").opacity(0.3) : Color.black.opacity(0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(
                                isSelected ? Color.hexColor("40E0D0") : Color.white.opacity(0.2),
                                lineWidth: 1
                            )
                    )
            )
            .foregroundColor(isSelected ? Color.hexColor("40E0D0") : .white.opacity(0.8))
        }
    }
    
    private func compactTagButton(title: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 11, weight: .medium))
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(isSelected ? Color.hexColor("40E0D0").opacity(0.3) : Color.black.opacity(0.3))
                        .overlay(
                            RoundedRectangle(cornerRadius: 6)
                                .stroke(
                                    isSelected ? Color.hexColor("40E0D0") : Color.white.opacity(0.2),
                                    lineWidth: 1
                                )
                        )
                )
                .foregroundColor(isSelected ? Color.hexColor("40E0D0") : .white.opacity(0.8))
        }
    }
    
    // MARK: - 辅助方法
    
    private func toggleTag(_ tag: String) {
        if selectedTags.contains(tag) {
            selectedTags.removeAll { $0 == tag }
        } else {
            selectedTags.append(tag)
        }
        onFilterChanged()
    }
}

/// 标签选择器弹窗
struct EATagSelectorSheet: View {
    let availableTags: [String]
    @Binding var selectedTags: [String]
    let onSelectionChanged: () -> Void
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 16) {
                // 已选标签区域
                if !selectedTags.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("已选标签 (\(selectedTags.count))")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                            ForEach(selectedTags, id: \.self) { tag in
                                tagChip(title: tag, isSelected: true) {
                                    selectedTags.removeAll { $0 == tag }
                                }
                            }
                        }
                    }
                    .padding()
                    .background(Color.black.opacity(0.3))
                    .cornerRadius(12)
                }
                
                // 可选标签区域
                VStack(alignment: .leading, spacing: 8) {
                    Text("热门标签")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                        ForEach(availableTags, id: \.self) { tag in
                            tagChip(title: tag, isSelected: selectedTags.contains(tag)) {
                                if selectedTags.contains(tag) {
                                    selectedTags.removeAll { $0 == tag }
                                } else {
                                    selectedTags.append(tag)
                                }
                            }
                        }
                    }
                }
                
                Spacer()
            }
            .padding()
            .background(Color.black)
            .navigationTitle("选择标签")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        onSelectionChanged()
                        dismiss()
                    }
                    .foregroundColor(Color.hexColor("40E0D0"))
                }
            }
        }
    }
    
    private func tagChip(title: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Text("#\(title)")
                .font(.system(size: 12, weight: .medium))
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isSelected ? Color.blue.opacity(0.2) : Color.black.opacity(0.3))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(
                                    isSelected ? Color.blue : Color.white.opacity(0.2),
                                    lineWidth: 1
                                )
                        )
                )
                .foregroundColor(isSelected ? .blue : .white.opacity(0.8))
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview

#Preview {
    EACommunityFilterBar(
        selectedCategory: .constant(nil),
        selectedTags: .constant([]),
        availableCategories: ["general", "achievement", "challenge"],
        popularTags: ["习惯养成", "运动", "阅读", "冥想", "学习"],
        onFilterChanged: {},
        onClearFilters: {}
    )
    .background(Color.black)
} 