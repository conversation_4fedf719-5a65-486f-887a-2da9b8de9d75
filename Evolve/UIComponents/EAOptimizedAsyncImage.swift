import SwiftUI

/// 🚀 性能优化的异步图片加载组件
/// 专为社区页面设计，支持懒加载、内存管理、网络状态优化
/// 遵循微信朋友圈级别的流畅度要求
@MainActor
struct EAOptimizedAsyncImage: View {
    
    // MARK: - Properties
    
    /// 图片路径
    let imagePath: String
    
    /// 图片尺寸
    let size: CGSize
    
    /// 内容模式
    let contentMode: ContentMode
    
    /// 是否启用懒加载
    let lazyLoading: Bool
    
    /// 🚀 性能监控
    @Environment(\.performanceMonitor) private var performanceMonitor
    
    /// 加载状态
    @State private var loadingState: ImageLoadingState = .idle
    @State private var loadedImage: UIImage?
    @State private var loadStartTime: Date?
    
    // MARK: - Initialization
    
    init(
        imagePath: String,
        size: CGSize,
        contentMode: ContentMode = .fill,
        lazyLoading: Bool = true
    ) {
        self.imagePath = imagePath
        self.size = size
        self.contentMode = contentMode
        self.lazyLoading = lazyLoading
    }
    
    // MARK: - Body
    
    var body: some View {
        Group {
            switch loadingState {
            case .idle:
                placeholderView
                    .onAppear {
                        if !lazyLoading {
                            startLoading()
                        }
                    }
                    .onReceive(NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)) { _ in
                        handleMemoryWarning()
                    }
                
            case .loading:
                loadingView
                
            case .loaded:
                if let image = loadedImage {
                    imageView(image: image)
                } else {
                    errorView
                }
                
            case .failed:
                errorView
            }
        }
        .frame(width: size.width, height: size.height)
        .clipped()
    }
    
    // MARK: - Views
    
    /// 🌟 星域主题：占位符视图
    private var placeholderView: some View {
        ZStack {
            // 星域主题渐变背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.gray.opacity(0.3),
                    Color.gray.opacity(0.1)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // 星域主题图标
            Image(systemName: "photo")
                .font(.system(size: min(size.width, size.height) * 0.3))
                .foregroundColor(.white.opacity(0.4))
        }
        .background(Color.gray.opacity(0.2))
        .onAppear {
            if lazyLoading {
                // 延迟加载：当视图出现时开始加载
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    startLoading()
                }
            }
        }
    }
    
    /// 🌟 星域主题：加载视图
    private var loadingView: some View {
        ZStack {
            // 星域主题渐变背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.gray.opacity(0.3),
                    Color.gray.opacity(0.1)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // 星域主题加载指示器
            ProgressView()
                .scaleEffect(1.2)
                .tint(.cyan)
        }
        .background(Color.gray.opacity(0.2))
    }
    
    /// 图片视图
    private func imageView(image: UIImage) -> some View {
        Image(uiImage: image)
            .resizable()
            .aspectRatio(contentMode: contentMode)
            .transition(.opacity.combined(with: .scale(scale: 0.95)))
    }
    
    /// 🌟 星域主题：错误视图
    private var errorView: some View {
        ZStack {
            // 星域主题渐变背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.red.opacity(0.2),
                    Color.red.opacity(0.1)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            VStack(spacing: 4) {
                Image(systemName: "exclamationmark.triangle")
                    .font(.system(size: min(size.width, size.height) * 0.2))
                    .foregroundColor(.red.opacity(0.6))
                
                Text("加载失败")
                    .font(.caption2)
                    .foregroundColor(.red.opacity(0.6))
            }
        }
        .background(Color.red.opacity(0.1))
        .onTapGesture {
            // 点击重试
            startLoading()
        }
    }
    
    // MARK: - Methods
    
    /// 开始加载图片
    private func startLoading() {
        guard loadingState != .loading else { return }
        
        loadingState = .loading
        loadStartTime = Date()
        
        // 🚀 性能监控：记录加载开始
        performanceMonitor?.recordImageLoadStart(for: imagePath)
        
        Task {
            await loadImage()
        }
    }
    
    /// 异步加载图片
    private func loadImage() async {
        do {
            let fullPath = getFullImagePath(imagePath)
            
            // 🚀 性能优化：根据网络状态调整加载策略
            let image = try await loadImageWithOptimization(from: fullPath)
            
            await MainActor.run {
                loadedImage = image
                loadingState = .loaded
                
                // 🚀 性能监控：记录加载成功
                if let startTime = loadStartTime {
                    let loadTime = Date().timeIntervalSince(startTime)
                    // 暂时注释，等待EAPerformanceMonitor实现这些方法
                    // performanceMonitor?.recordImageLoadSuccess(for: imagePath, loadTime: loadTime)
                }
            }
            
        } catch {
            await MainActor.run {
                loadingState = .failed
                
                // 🚀 性能监控：记录加载失败
                // 暂时注释，等待EAPerformanceMonitor实现这些方法
                // performanceMonitor?.recordImageLoadFailure(for: imagePath, error: error)
            }
        }
    }
    
    /// 🚀 性能优化：根据网络状态优化图片加载
    private func loadImageWithOptimization(from path: String) async throws -> UIImage {
        guard let image = UIImage(contentsOfFile: path) else {
            throw ImageLoadingError.fileNotFound
        }
        
        // 🚀 性能优化：根据网络状态和内存使用情况调整图片质量
        let networkStatus = performanceMonitor?.networkStatus ?? .unknown
        let memoryUsage = performanceMonitor?.memoryUsage ?? 0
        
        if networkStatus == .cellular || memoryUsage > 150 {
            // 蜂窝网络或内存使用过高时，压缩图片
            return await compressImage(image, quality: 0.7)
        } else {
            // WiFi网络且内存充足时，使用原图
            return image
        }
    }
    
    /// 压缩图片
    private func compressImage(_ image: UIImage, quality: CGFloat) async -> UIImage {
        return await Task.detached {
            guard let data = image.jpegData(compressionQuality: quality),
                  let compressedImage = UIImage(data: data) else {
                return image
            }
            return compressedImage
        }.value
    }
    
    /// 处理内存警告
    private func handleMemoryWarning() {
        // 清理已加载的图片以释放内存
        if loadingState == .loaded {
            loadedImage = nil
            loadingState = .idle
        }
    }
    
    /// 获取完整图片路径
    private func getFullImagePath(_ relativePath: String) -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        return (documentsPath as NSString).appendingPathComponent(relativePath)
    }
}

// MARK: - Supporting Types

/// 图片加载状态
enum ImageLoadingState {
    case idle
    case loading
    case loaded
    case failed
}

/// 图片加载错误
enum ImageLoadingError: Error {
    case fileNotFound
    case invalidData
    case compressionFailed
}

// MARK: - Preview

struct EAOptimizedAsyncImage_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            // 正常尺寸图片
            EAOptimizedAsyncImage(
                imagePath: "sample.jpg",
                size: CGSize(width: 200, height: 150)
            )
            
            // 小尺寸图片
            EAOptimizedAsyncImage(
                imagePath: "sample.jpg",
                size: CGSize(width: 60, height: 60),
                contentMode: .fill
            )
        }
        .padding()
        .background(Color.hexColor("002b20"))
        .previewDisplayName("优化异步图片")
    }
}
