import SwiftUI

/// 数字宇宙社区帖子卡片 - 星域信标设计
/// 🌟 全面升级：星域数字宇宙主题 + 性能优化 + 微信朋友圈级流畅度
/// 基于数字宇宙主题，将帖子呈现为星际探索者发布的宇宙信标
/// 支持星际能量显示、宇宙等级标识、荧光青色渐变边框、星光粒子动画等特效
/// 设计面向18-30岁年轻用户，具有强烈的科技感和未来感
/// 🚀 性能优化：图片懒加载、内存管理、滑动流畅度优化
@MainActor
struct EACommunityPostCard: View {

    // MARK: - Properties

    /// 帖子数据
    let post: EACommunityPost

    /// 是否已点赞
    @State private var isLiked: Bool = false

    /// 点赞数量
    @State private var currentLikeCount: Int = 0

    /// 🔑 修复：评论数量的本地状态（支持实时更新）
    @State private var currentCommentCount: Int = 0

    /// 卡片样式
    let style: EACommunityPostCardStyle

    /// 🚀 性能优化：图片加载状态管理
    @State private var imageLoadingStates: [String: Bool] = [:]
    @State private var imageLoadErrors: [String: Bool] = [:]

    /// 🌟 星域主题：动画状态
    @State private var stellarGlowOpacity: Double = 0.6
    @State private var energyFlowOffset: CGFloat = 0

    /// 点赞回调
    let onLike: ((EACommunityPost) async throws -> Void)?

    /// 帖子点击回调
    let onPostTap: ((EACommunityPost) -> Void)?

    /// 用户头像点击回调
    let onUserTap: (() -> Void)?

    /// 图片点击回调
    let onImageTap: ((String, Int) -> Void)?

    /// 评论回调
    let onComment: ((EACommunityPost) -> Void)?
    
    /// 分享回调
    let onShare: ((EACommunityPost) -> Void)?
    
    /// 用户头像点击回调
    let onUserProfileTap: () -> Void
    
    /// 删除回调
    let onDelete: ((EACommunityPost) async -> Void)?
    
    /// 是否可以删除
    let canDelete: Bool
    
    /// 内部状态
    @State private var isProcessingLike: Bool = false
    @State private var showDeleteAlert: Bool = false
    @State private var isDeleting: Bool = false
    @State private var showImageViewer: Bool = false
    @State private var selectedImageIndex: Int = 0
    
    // MARK: - 🔑 性能优化：预计算布局值，避免重复计算

    /// 预计算的缩略图大小
    private let thumbnailSize: CGFloat = 60

    /// 预计算的卡片内边距
    private let cardPadding: CGFloat = 16

    /// 预计算的用户头像大小
    private let avatarSize: CGFloat = 40

    /// 预计算的按钮区域高度
    private let actionButtonHeight: CGFloat = 44

    @Environment(\.colorScheme) private var colorScheme

    // MARK: - 🔑 性能优化：缓存计算结果

    /// 缓存的用户称呼
    @State private var cachedExplorerTitle: String = ""

    /// 缓存的时间显示
    @State private var cachedTimeAgo: String = ""

    /// 是否已初始化缓存
    @State private var isCacheInitialized: Bool = false
    
    // MARK: - Initialization
    
    init(
        post: EACommunityPost,
        isLiked: Bool = false,
        onLike: ((EACommunityPost) async throws -> Void)? = nil,
        onComment: ((EACommunityPost) -> Void)? = nil,
        onShare: ((EACommunityPost) -> Void)? = nil,
        onPostTap: ((EACommunityPost) -> Void)? = nil,
        onDelete: ((EACommunityPost) async -> Void)? = nil,
        canDelete: Bool = false,
        style: EACommunityPostCardStyle = .standard,
        onUserTap: (() -> Void)? = nil,
        onImageTap: ((String, Int) -> Void)? = nil,
        onUserProfileTap: @escaping () -> Void
    ) {
        self.post = post
        self.onLike = onLike
        self.onComment = onComment
        self.onShare = onShare
        self.onPostTap = onPostTap
        self.onDelete = onDelete
        self.canDelete = canDelete
        self.style = style
        self.onUserTap = onUserTap
        self.onImageTap = onImageTap
        self.onUserProfileTap = onUserProfileTap
        
        // 初始化状态
        self._isLiked = State(initialValue: isLiked)
        self._currentLikeCount = State(initialValue: post.likeCount)
        self._currentCommentCount = State(initialValue: post.commentCount)
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 用户信息区域
            userInfoSection
            
            // 内容区域（可点击进入详情）
            contentSection
            
            // 图片区域（使用EAImageGridView）
            if !post.imageURLs.isEmpty {
                imageSection
                    .padding(.horizontal, 16)
                    .padding(.bottom, 16)
            }
            
            // 互动按钮区域（不可点击进入详情）
            actionButtonsSection
        }
        .background(cardBackground)
        .overlay(cardBorder)
        .cornerRadius(16)
        .shadow(color: .cyan.opacity(0.1), radius: 8, x: 0, y: 4)
        // 🔑 核心修复：整个卡片支持点击，但交互按钮不响应
        .contentShape(Rectangle())
        .onTapGesture {
            onPostTap?(post)
        }
        .alert("确认删除", isPresented: $showDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                Task {
                    await handleDelete()
                }
            }
        } message: {
            Text("确定要删除这条帖子吗？删除后无法恢复。")
        }
        .sheet(isPresented: $showImageViewer) {
            imageViewer
        }
        .onAppear {
            // 🔑 性能优化：初始化缓存和数据同步
            initializeCardData()
            setupDataSyncListeners()
        }
        .onChange(of: post.commentCount) { _, newValue in
            // 🔑 修复：监听评论数量变化并实时更新
            currentCommentCount = newValue
        }
    }
    
    private var userInfoSection: some View {
        HStack(spacing: 12) {
            // 🔑 性能优化：使用预计算尺寸的头像
            userAvatarView
                .onTapGesture {
                    onUserProfileTap()
                }
            
            VStack(alignment: .leading, spacing: 4) {
                // 用户名
                Text(post.getAuthorUsername())
                    .font(.system(size: 15, weight: .semibold))
                    .foregroundColor(.white)
                
                // 🔑 修复：只显示分类标签，移除时间和能力等级
                if post.category == "challenge" {
                    Text("挑战中")
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(.yellow)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.yellow.opacity(0.15))
                        .clipShape(Capsule())
                }
            }
            
            Spacer()
            
            // 🔑 性能优化：使用缓存的称呼和时间
            VStack(alignment: .trailing, spacing: 4) {
                // 能力等级称呼（使用缓存）
                Text(cachedExplorerTitle)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.cyan)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.cyan.opacity(0.15))
                    .clipShape(Capsule())

                // 发帖时间（使用缓存）
                Text(cachedTimeAgo)
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
            }
            
            Spacer(minLength: 0)
            
            // 删除按钮（仅帖子作者可见）
            if canDelete {
                Button(action: {
                    showDeleteAlert = true
                }) {
                    Image(systemName: "trash")
                        .font(.system(size: 16))
                        .foregroundColor(.red.opacity(0.8))
                        .frame(width: 32, height: 32)
                        .background(Color.red.opacity(0.1))
                        .clipShape(Circle())
                }
                .disabled(isDeleting)
            }
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
        .padding(.bottom, 12)
    }
    
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 帖子内容
            Text(post.content)
                .font(.system(size: 15, weight: .regular))
                .foregroundColor(.white.opacity(0.9))
                .lineLimit(6)
                .multilineTextAlignment(.leading)
                .padding(.horizontal, 16)
            
            // 习惯标签（如果有）
            if let habitName = post.habitName, !habitName.isEmpty {
                HStack {
                    Label(habitName, systemImage: "target")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.cyan)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color.cyan.opacity(0.15))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 6)
                                        .stroke(Color.cyan.opacity(0.3), lineWidth: 1)
                                )
                        )
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
            }
        }
        .padding(.bottom, 4) // 🔑 修复：增加内容与按钮区域的间距
        .contentShape(Rectangle()) // 定义点击区域
        .onTapGesture {
            // 🔑 核心修复：只在内容区域响应点击进入详情
            onPostTap?(post)
        }
    }
    
    private var imageSection: some View {
        EAImageGridView(
            imagePaths: post.imageURLs,
            onImageTap: { imagePath, index in
                selectedImageIndex = index
                showImageViewer = true
            }
        )
        .frame(maxHeight: 200) // 限制最大高度
    }
    
    private var actionButtonsSection: some View {
        HStack(spacing: 24) {
            // 🔑 核心修复：使用专业的EALikeButton组件
            EALikeButton(
                isLiked: $isLiked,
                likeCount: currentLikeCount,
                size: .medium,
                isEnabled: !isProcessingLike
            ) {
                Task {
                    await handleLikeToggle()
                }
            }
            .allowsHitTesting(true) // 🔑 修复：确保按钮可以响应点击
            
            // 评论按钮
            Button(action: {
                onComment?(post)
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "bubble.left")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                    
                    // 🔑 修复：使用本地状态显示评论数量
                    if currentCommentCount > 0 {
                        Text("\(currentCommentCount)")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
            .buttonStyle(PlainButtonStyle())
            .allowsHitTesting(true) // 🔑 修复：确保按钮可以响应点击
            
            // 分享按钮
            Button(action: {
                onShare?(post)
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                    
                    if post.shareCount > 0 {
                        Text("\(post.shareCount)")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
            .buttonStyle(PlainButtonStyle())
            .allowsHitTesting(true) // 🔑 修复：确保按钮可以响应点击
            
            Spacer()
            
            // 🔑 修复：能量等级显示，使用宇宙风格
            if post.energyLevel > 0 {
                HStack(spacing: 4) {
                    Image(systemName: "bolt.fill")
                        .font(.system(size: 12))
                        .foregroundColor(.yellow)
                    
                    Text("Lv.\(post.energyLevel)")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.yellow)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color.yellow.opacity(0.15))
                        .overlay(
                            RoundedRectangle(cornerRadius: 6)
                                .stroke(Color.yellow.opacity(0.3), lineWidth: 1)
                        )
                )
            }
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
        .padding(.top, 8) // 🔑 修复：增加按钮区域顶部间距
        .allowsHitTesting(true) // 🔑 修复：确保整个按钮区域可以响应交互
    }
    
    /// 🌟 星域主题：增强的卡片背景
    private var cardBackground: some View {
        ZStack {
            // 🌟 基础星域渐变背景
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color.hexColor("001a14").opacity(0.95), location: 0.0),
                    .init(color: Color.hexColor("002b20").opacity(0.9), location: 0.3),
                    .init(color: Color.hexColor("003d2a").opacity(0.85), location: 0.7),
                    .init(color: Color.hexColor("001a14").opacity(0.95), location: 1.0)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // 🌟 星域主题：毛玻璃效果增强数字宇宙感
            RoundedRectangle(cornerRadius: style.cornerRadius)
                .fill(.ultraThinMaterial)
                .opacity(0.8)

            // 🌟 星域主题：能量流动效果
            RoundedRectangle(cornerRadius: style.cornerRadius)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.cyan.opacity(0.05),
                            Color.clear,
                            Color.blue.opacity(0.03),
                            Color.clear,
                            Color.purple.opacity(0.05)
                        ]),
                        startPoint: .init(x: energyFlowOffset, y: 0),
                        endPoint: .init(x: energyFlowOffset + 0.3, y: 1)
                    )
                )
                .animation(.linear(duration: 3.0).repeatForever(autoreverses: false), value: energyFlowOffset)
        }
        .onAppear {
            // 🌟 启动能量流动动画
            withAnimation(.linear(duration: 3.0).repeatForever(autoreverses: false)) {
                energyFlowOffset = 1.0
            }
        }
    }
    
    /// 🌟 星域主题：荧光青色渐变边框
    private var cardBorder: some View {
        RoundedRectangle(cornerRadius: style.cornerRadius)
            .stroke(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.cyan.opacity(0.4),
                        Color.blue.opacity(0.3),
                        Color.purple.opacity(0.4),
                        Color.cyan.opacity(0.2)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ),
                lineWidth: 1.5
            )
            .shadow(color: Color.cyan.opacity(0.2), radius: 4, x: 0, y: 0)
    }
    
    private var imageViewer: some View {
        Group {
            if !post.imageURLs.isEmpty && selectedImageIndex < post.imageURLs.count {
                NavigationView {
                    AsyncImage(url: URL(fileURLWithPath: getFullImagePath(post.imageURLs[selectedImageIndex]))) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                    } placeholder: {
                        ProgressView()
                            .scaleEffect(1.5)
                            .tint(.cyan)
                    }
                    .background(Color.black)
                    .navigationTitle("图片 \(selectedImageIndex + 1)/\(post.imageURLs.count)")
                    .navigationBarTitleDisplayMode(.inline)
                    .navigationBarBackButtonHidden(true)
                    .overlay(alignment: .topLeading) {
                        Button("关闭") {
                            showImageViewer = false
                        }
                        .foregroundColor(.cyan)
                        .padding()
                        .background(Color.black.opacity(0.6))
                        .cornerRadius(8)
                        .padding()
                    }
                }
            } else {
                Text("图片加载失败")
                    .foregroundColor(.gray)
            }
        }
    }
    
    private func handleLikeToggle() async {
        guard !isProcessingLike else { return }
        
        isProcessingLike = true
        defer { isProcessingLike = false }
        
        do {
            try await onLike?(post)
            // 切换本地状态
            isLiked.toggle()
            // 更新点赞数量
            currentLikeCount = isLiked ? currentLikeCount + 1 : max(0, currentLikeCount - 1)
        } catch {
            // 错误处理 - 可以在这里显示错误提示
        }
    }
    
    private func handleDelete() async {
        isDeleting = true
        defer { isDeleting = false }
        
        await onDelete?(post)
    }
    
    private func formatTimeAgo(_ date: Date) -> String {
        let now = Date()
        let timeInterval = now.timeIntervalSince(date)
        
        if timeInterval < 60 {
            return "刚刚"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)分钟前"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)小时前"
        } else {
            let days = Int(timeInterval / 86400)
            return "\(days)天前"
        }
    }
    
    /// 🔑 修复：获取用户探索者称呼的方法（与星际档案一致）
    private func getUserExplorerTitle() -> String {
        // 🔑 修复：优先从作者社交档案获取称呼
        if let explorerTitle = post.authorSocialProfile?.explorerTitle, !explorerTitle.isEmpty {
            return explorerTitle
        }
        
        // 🔑 修复：如果没有社交档案，按照星际档案的逻辑计算称呼
        let energyLevel = post.energyLevel
        switch energyLevel {
        case 1...3:
            return "新手探索者"
        case 4...7:
            return "星际旅者"
        case 8...12:
            return "宇宙航行者"
        case 13...16:
            return "星系守护者"
        case 17...20:
            return "宇宙大师"
        default:
            return energyLevel > 20 ? "传奇探索者" : "新手探索者"
        }
    }
    
    private func getFullImagePath(_ relativePath: String) -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        return (documentsPath as NSString).appendingPathComponent(relativePath)
    }

    // MARK: - 🔑 性能优化方法

    /// 初始化卡片数据和缓存
    private func initializeCardData() {
        guard !isCacheInitialized else { return }

        // 初始化基础数据
        currentLikeCount = post.getActiveLikesCount()
        currentCommentCount = post.commentCount
        isLiked = false // 这里应该从用户数据中获取实际状态

        // 缓存计算结果
        cachedExplorerTitle = calculateExplorerTitle()
        cachedTimeAgo = formatTimeAgo(post.creationDate)

        isCacheInitialized = true
    }

    /// 设置数据同步监听器
    private func setupDataSyncListeners() {
        // 🔑 优化：使用Repository模式进行数据同步，符合项目架构规范
        // 这里可以通过Repository获取最新数据状态
    }

    /// 计算用户探索者称呼（缓存版本）
    private func calculateExplorerTitle() -> String {
        // 优先从作者社交档案获取称呼
        if let explorerTitle = post.authorSocialProfile?.explorerTitle, !explorerTitle.isEmpty {
            return explorerTitle
        }

        // 按照星际档案的逻辑计算称呼
        let energyLevel = post.energyLevel
        switch energyLevel {
        case 1...3:
            return "新手探索者"
        case 4...7:
            return "星际旅者"
        case 8...12:
            return "宇宙航行者"
        case 13...16:
            return "星系守护者"
        case 17...20:
            return "宇宙大师"
        default:
            return energyLevel > 20 ? "传奇探索者" : "新手探索者"
        }
    }

    /// 🌟 星域主题：优化的用户头像视图（与"我的"页面数据一致）
    private var userAvatarView: some View {
        ZStack {
            // 🌟 星域主题：荧光青色光环效果
            Circle()
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.cyan.opacity(stellarGlowOpacity),
                            Color.blue.opacity(stellarGlowOpacity * 0.6),
                            Color.purple.opacity(stellarGlowOpacity * 0.3)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 2
                )
                .frame(width: avatarSize + 4, height: avatarSize + 4)
                .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: stellarGlowOpacity)

            // 🔑 修复：使用EAAvatarView组件，确保与"我的"页面头像一致
            if let author = post.author {
                EAAvatarView(avatarData: author.avatarData, size: avatarSize)
                    .clipShape(Circle())
            } else {
                // 默认头像 - 星域主题风格
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.blue.opacity(0.7),
                                Color.purple.opacity(0.7)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: avatarSize, height: avatarSize)
                    .overlay(
                        Text(String(post.getAuthorUsername().prefix(1)))
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                    )
            }
        }
        .onAppear {
            // 🌟 启动星域光环动画
            withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
                stellarGlowOpacity = 0.3
            }
        }
    }
}

// MARK: - Card Style

/// 帖子卡片样式
enum EACommunityPostCardStyle {
    case standard
    case compact
    case featured
    case detail // 详情页样式，禁用动画效果
    
    var cardCornerRadius: CGFloat {
        switch self {
        case .standard:
            return EAAppConstants.Community.PostCard.cardCornerRadius
        case .compact:
            return EAAppConstants.Community.PostCard.cardCornerRadius - 4
        case .featured:
            return EAAppConstants.Community.PostCard.cardCornerRadius + 4
        case .detail:
            return EAAppConstants.Community.PostCard.cardCornerRadius
        }
    }
}

// MARK: - Preview

struct EACommunityPostCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            // 带图片的帖子
            EACommunityPostCard(
                post: {
                    let post = EACommunityPost(
                        content: "今天完成了晨跑习惯，感觉整个人都充满了能量！坚持就是胜利，一起加油吧！ 🏃‍♂️✨",
                        habitName: "晨跑习惯",
                        category: "健康",
                        energyLevel: 8
                    )
                    post.imageURLs = ["sample1.jpg", "sample2.jpg", "sample3.jpg"]
                    post.likeCount = 42
                    post.commentCount = 8
                    post.shareCount = 3
                    return post
                }(),
                isLiked: false,
                onUserProfileTap: {}
            )
            
            // 纯文本帖子
            EACommunityPostCard(
                post: {
                    let post = EACommunityPost(
                        content: "分享一个小心得：习惯养成最重要的是坚持，不要追求完美，每天进步一点点就够了。",
                        category: "经验分享",
                        energyLevel: 6
                    )
                    post.likeCount = 28
                    post.commentCount = 12
                    return post
                }(),
                isLiked: true,
                onUserProfileTap: {}
            )
        }
        .padding()
        .background(Color.hexColor("002b20"))
        .previewDisplayName("社区帖子卡片")
    }
} 